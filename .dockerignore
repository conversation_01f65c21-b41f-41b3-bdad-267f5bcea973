# Build outputs
**/bin/
**/obj/
**/out/

# IDE files
.vs/
.vscode/
*.user
*.suo
*.userosscache
*.sln.docstates

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Documentation
*.md
CONFIGURATION.md
MIGRATION_STRATEGY.md
README.md
TESTING_CHECKLIST.md

# Frontend files (not needed for backend builds)
frontend/
node_modules/

# Terraform
terraform/

# Test files
test-compression-api.http

# Logs
*.log
firestore-debug.log

# Docker files (except the ones being built)
docker-compose.yml
Dockerfile*

# Temporary files
*.tmp
*.temp

# Package files
*.nupkg
*.snupkg

# Other
issues.txt
todo.md
gallery_tuner.ico
firebase.json
run-local-*.sh
