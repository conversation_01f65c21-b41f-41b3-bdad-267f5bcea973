#!/bin/bash

# Quick test script to verify Secret Manager integration works

set -e

echo "🧪 Testing Secret Manager Integration"
echo "======================================"
echo ""

# Check if gcloud is authenticated
echo "1. Checking gcloud authentication..."
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ You need to authenticate with gcloud first:"
    echo "   gcloud auth login"
    echo "   gcloud auth application-default login"
    exit 1
fi
echo "✅ gcloud authenticated"

# Check project access
echo ""
echo "2. Checking project access..."
PROJECT_ID="tranquil-bison-465923-v9"
if ! gcloud projects describe $PROJECT_ID >/dev/null 2>&1; then
    echo "❌ Cannot access project $PROJECT_ID"
    exit 1
fi
echo "✅ Project access confirmed"

# Check if secrets exist
echo ""
echo "3. Checking if development secrets exist..."
SECRETS_TO_CHECK=(
    "google-client-secret-dev"
    "stripe-secret-key-dev" 
    "stripe-webhook-secret-dev"
)

MISSING_SECRETS=()
for secret in "${SECRETS_TO_CHECK[@]}"; do
    if gcloud secrets describe $secret --project=$PROJECT_ID >/dev/null 2>&1; then
        echo "✅ Secret exists: $secret"
    else
        echo "❌ Secret missing: $secret"
        MISSING_SECRETS+=($secret)
    fi
done

if [ ${#MISSING_SECRETS[@]} -gt 0 ]; then
    echo ""
    echo "⚠️  Missing secrets detected. Run this to create them:"
    echo "   ./deployment/setup-secrets.sh dev"
    echo ""
    echo "Or create them manually:"
    for secret in "${MISSING_SECRETS[@]}"; do
        echo "   echo -n 'your-secret-value' | gcloud secrets versions add $secret --data-file=- --project=$PROJECT_ID"
    done
    exit 1
fi

# Test Secret Manager permissions
echo ""
echo "4. Testing Secret Manager access..."
if gcloud secrets versions access latest --secret="google-client-secret-dev" --project=$PROJECT_ID >/dev/null 2>&1; then
    echo "✅ Can access secrets"
else
    echo "❌ Cannot access secrets - check IAM permissions"
    echo "   You need: roles/secretmanager.secretAccessor"
    exit 1
fi

# Test building the projects
echo ""
echo "5. Testing project builds..."
if dotnet build shared/shared.csproj --verbosity quiet; then
    echo "✅ Shared project builds successfully"
else
    echo "❌ Shared project build failed"
    exit 1
fi

if dotnet build api-server/api-server.csproj --verbosity quiet; then
    echo "✅ API server builds successfully"
else
    echo "❌ API server build failed"
    exit 1
fi

if dotnet build worker-service/worker-service.csproj --verbosity quiet; then
    echo "✅ Worker service builds successfully"
else
    echo "❌ Worker service build failed"
    exit 1
fi

echo ""
echo "🎉 All tests passed!"
echo ""
echo "Your Secret Manager integration is ready to use:"
echo "  • Secrets are accessible from Google Cloud"
echo "  • Projects build without errors"
echo "  • Configuration provider will load secrets at startup"
echo ""
echo "To run your services:"
echo "  cd api-server && dotnet run"
echo "  cd worker-service && dotnet run"
echo ""
echo "Secrets will be loaded automatically from Google Secret Manager! 🔐"
