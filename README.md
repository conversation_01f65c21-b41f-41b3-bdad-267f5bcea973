Project Brief: "VidCompressor" Video Compression Service
Product Vision
VidCompressor is a web service designed for Google Photos users who need to free up storage space without deleting their memories. The service will provide a simple, secure, and intuitive interface for users to select large videos from their Google Photos library, compress them to a user-selected quality, and replace the originals. The product will operate on a freemium model, offering a basic free tier and a premium subscription for power users.

Core User Flow
Authentication: The user lands on the application and logs in securely using their Google account ("Sign in with Google"). They will be asked to grant the application permission to access their Google Photos library.

Video Library Browsing: The user is presented with a familiar, gallery-style interface that displays the videos from their Google Photos library, with larger files clearly highlighted.

Selection & Configuration: The user selects one or more videos to compress. They choose a desired compression quality (e.g., "High Quality - 1080p," "Space Saver - 720p").

Queue & Process: The selected videos are added to a processing queue. The UI updates in real-time to show the status of each video (e.g., "Queued," "Compressing," "Complete," "Error").

Completion: Once a video is compressed, the newly compressed file is uploaded back to the user's Google Photos library. The user is then prompted to approve the deletion of the original, larger file to reclaim storage space.

System Architecture & Technology Stack
This is a containerized microservices architecture designed for scalability, resilience, and efficient resource management. The entire stack will be orchestrated using Kubernetes.

Component

Technology & Rationale

Frontend Application

Part 1:
React with TypeScript using the MUI component library. This provides a robust, type-safe foundation for a responsive UI and ensures visual consistency with Google's Material Design.

Part 2:
Chrome Extension built with TypeScript. It will inject UI elements (buttons, modals) into the Google Photos web page. This avoids recreating the gallery view and provides a seamless user experience. We will use a lightweight framework like React to build the extension's UI components.

Backend API Server

C# with .NET 8 (ASP.NET Core). This will serve as the central hub of the application. Its high performance and asynchronous capabilities are perfect for handling API requests, managing user data, and enqueuing jobs without getting blocked.

Job Queue / Message Broker

Redis. We will use Redis for its speed as a lightweight message broker. It will manage the queue of video compression jobs, decoupling the API server from the processing workers. A library like Hangfire or a custom solution with StackExchange.Redis can be used.

Video Processing Worker

A dedicated .NET background service whose sole job is to pull tasks from the Redis queue. This service will call the FFmpeg command-line tool to perform the actual video compression.

Orchestration & Hosting

Kubernetes on Google Kubernetes Engine (GKE). We will leverage GKE for its managed environment, scalability, and proximity to Google's services. This reduces latency for Google Photos API calls.

Primary Database

PostgreSQL. A reliable, open-source relational database for storing user information, subscription status, job metadata, and payment records. We will use a managed instance on GCP (Cloud SQL) to simplify operations.

Key System Implementation Details
1. Authentication & User Management
Flow: Implement the OAuth 2.0 / OpenID Connect (OIDC) "Sign in with Google" flow.

Frontend: Use the @react-oauth/google library to handle the client-side authentication dance.

Backend: Use the Microsoft.AspNetCore.Authentication.JwtBearer package to validate the JWT received from the frontend. On successful validation, create a user record in the PostgreSQL database. This local record will be our "source of truth" for the user's subscription and activity.

2. Security & Secrets Management
All sensitive data (Google API secrets, database connection strings, Redis passwords) must not be in source code.

Implementation: Store these as Kubernetes Secrets. In GKE, we will leverage Google Secret Manager as the central source of truth and sync these secrets to our cluster using the Secrets Store CSI Driver. The .NET application will access them via mounted files or environment variables.

3. Monetization & Payments
Model: Freemium (e.g., 5 free compressions per month).

Provider: Integrate Stripe for handling all payment processing and subscription management.

Implementation: Use the Stripe.net NuGet package. The backend will listen for webhooks from Stripe (e.g., checkout.session.completed, customer.subscription.updated) to update the user's subscription status in our PostgreSQL database.

4. Real-Time UI Notifications
To provide live progress updates to the user, we will implement push notifications.

Implementation: Use SignalR for .NET. The backend API will host a SignalR hub. When a worker completes a job, it will notify the API, which will then push a message through the hub to the specific user's connected frontend client.

5. Job Resilience & Error Handling
Video processing can fail. The system must be resilient to this.

Retry Logic: Implement an automatic retry mechanism with exponential backoff for failed jobs.

Dead-Letter Queue (DLQ): After a predefined number of retries (e.g., 3-5), the failed job should be moved to a separate "dead-letter" queue in Redis. This prevents poison-pill messages and allows for manual inspection later.

6. Kubernetes & Hosting Strategy
GKE Mode: We should start with GKE Autopilot to optimize for cost and reduce operational overhead.

Cost Optimization: The video processing workers are prime candidates for running on Spot VMs. We will configure a dedicated node pool for these workers to leverage the significant cost savings.

CI/CD: A CI/CD pipeline (e.g., using GitHub Actions) should be set up to automatically build and deploy container images to GKE upon commits to the main branch.

Pre-Development Implementation Planning
Before development begins, the following areas must be planned to ensure a robust and maintainable system.

1. Observability Strategy
Centralized Logging: All services (frontend, backend, workers) must output structured, JSON-formatted logs. These will be aggregated in Google Cloud Logging for centralized searching and analysis.

Metrics & Monitoring: We will use Prometheus for metrics collection and Grafana for dashboards. Key metrics to monitor include API latency/error rates, Redis queue depth, and worker job throughput.

Distributed Tracing: Implement OpenTelemetry across the .NET API and workers to trace requests from initial user interaction to final job completion. This is critical for debugging performance in our distributed environment.

2. Local Development Experience
To ensure developer productivity, a Docker Compose environment will be created. This will allow engineers to run the entire application stack (React, .NET API, PostgreSQL, Redis, Worker) on their local machines with a single command, simplifying testing and debugging.

3. FFmpeg Strategy
Compression Presets: We must define and test the exact FFmpeg command-line arguments for our compression tiers ("High Quality," "Space Saver," etc.). These presets will be stored in configuration and should balance quality, file size, and processing time.

Hardware Acceleration: To maximize performance and reduce cost, workers running on GPU-enabled Spot VMs must utilize hardware-accelerated encoding (NVENC). This requires a custom Docker image for the worker with the appropriate NVIDIA drivers and a version of FFmpeg compiled with NVENC support.

4. API & Data Contracts
API Specification: The backend team will define the API using the OpenAPI (Swagger) standard. This contract will be shared with the frontend team to enable parallel development.

Database Schema: The initial schema for the PostgreSQL database must be designed, defining the tables, columns, relationships, and constraints for Users, Subscriptions, Jobs, etc.

5. Google Photos API Integration
Quota Management: The application must be designed to gracefully handle Google's API rate limits (default 10,000 requests/day). We will implement client-side and server-side logic to manage request frequency and have a plan to request a quota increase as our user base grows.

Permission Handling: The application must correctly handle cases where a user revokes API access from their Google Account settings. The system should detect the resulting authorization errors, flag the user's account, and prompt for re-authentication.

6. Infrastructure as Code (IaC) & Deployment
Infrastructure Provisioning: All cloud resources on GCP (GKE Cluster, Cloud SQL for PostgreSQL, Memorystore for Redis, IAM roles, etc.) will be defined and managed using Terraform. This ensures our infrastructure is version-controlled, repeatable, and easy to modify.

Application Deployment: All applications deployed to Kubernetes (API server, workers) will be packaged and managed using Helm charts. This simplifies the deployment, configuration, and upgrade process for our services.

CI/CD
- use github actions for our CI/CD pipelines