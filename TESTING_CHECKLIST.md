# Testing Checklist: Google Transcoder API Migration

## Pre-Deployment Testing

### Infrastructure Testing
- [ ] Verify Google Cloud Storage buckets are created
- [ ] Verify Google Transcoder API is enabled
- [ ] Verify service account has correct permissions
- [ ] Test service account key authentication
- [ ] Verify IAM roles are properly assigned

### Database Testing
- [ ] Run database migration successfully
- [ ] Verify CompressionJob table is created with correct schema
- [ ] Verify User table has new columns (DefaultOverwriteOriginal, CompressionJobs)
- [ ] Test database indexes are created
- [ ] Verify foreign key relationships work correctly

### Backend API Testing
- [ ] Application starts without errors
- [ ] All dependencies are properly injected
- [ ] Google Cloud services initialize correctly
- [ ] Redis connection works
- [ ] Database connection works

## API Endpoint Testing

### Compression Job Creation
- [ ] POST /api/videos/{mediaItemId}/compress with valid data
- [ ] Verify job is created in database
- [ ] Verify job is queued in Redis
- [ ] Test with different quality settings (high, medium, low)
- [ ] Test with overwriteOriginal = true/false
- [ ] Test error handling for invalid media item ID
- [ ] Test error handling for invalid quality setting
- [ ] Test authorization requirements

### Job Status and Management
- [ ] GET /api/videos/compression-jobs/{jobId}/status
- [ ] GET /api/videos/compression-jobs (list all jobs)
- [ ] POST /api/videos/compression-jobs/{jobId}/cancel
- [ ] Test pagination for job listing
- [ ] Test filtering by user (security)
- [ ] Test error handling for non-existent jobs

## Background Service Testing

### Job Processing Workflow
- [ ] Background service starts and subscribes to Redis
- [ ] Job is picked up from queue
- [ ] Job status updates correctly through each phase:
  - [ ] Queued → DownloadingFromGooglePhotos
  - [ ] DownloadingFromGooglePhotos → UploadingToStorage
  - [ ] UploadingToStorage → TranscodingInProgress
  - [ ] TranscodingInProgress → DownloadingFromStorage
  - [ ] DownloadingFromStorage → UploadingToGooglePhotos
  - [ ] UploadingToGooglePhotos → Completed (or DeletingOriginal if overwrite)

### Google Cloud Integration
- [ ] Video download from Google Photos works
- [ ] Video upload to Cloud Storage input bucket works
- [ ] Transcoder job creation works
- [ ] Transcoder job monitoring works
- [ ] Video download from Cloud Storage output bucket works
- [ ] Video upload back to Google Photos works
- [ ] Storage cleanup works (files are deleted after processing)

### Error Handling
- [ ] Failed Google Photos download
- [ ] Failed Cloud Storage upload
- [ ] Failed Transcoder job creation
- [ ] Failed Transcoder job (transcoding error)
- [ ] Failed Cloud Storage download
- [ ] Failed Google Photos upload
- [ ] Job cancellation during different phases
- [ ] Network timeouts and retries

## Frontend Testing

### Compression Modal
- [ ] Modal opens when clicking compress button
- [ ] Modal opens for batch compression
- [ ] Quality selection works (high, medium, low)
- [ ] Overwrite original checkbox works
- [ ] Modal shows correct item count for batch operations
- [ ] Cancel button works
- [ ] Start compression button works

### User Experience
- [ ] Compression starts successfully
- [ ] Success/error notifications display correctly
- [ ] Batch compression processes all selected items
- [ ] Selection is cleared after batch compression
- [ ] Error handling for failed compression requests

## Integration Testing

### End-to-End Workflow
- [ ] Complete single video compression workflow
- [ ] Complete batch video compression workflow
- [ ] Test with different video formats and sizes
- [ ] Test with different quality settings
- [ ] Test overwrite vs. keep original scenarios
- [ ] Verify compression ratios are reasonable
- [ ] Verify processing times are acceptable

### Concurrent Operations
- [ ] Multiple users compressing simultaneously
- [ ] Multiple jobs from same user
- [ ] Job queue handling under load
- [ ] Database concurrency handling
- [ ] Cloud Storage concurrent access

## Performance Testing

### Load Testing
- [ ] Process 10 concurrent compression jobs
- [ ] Process 50 concurrent compression jobs
- [ ] Test with large video files (>1GB)
- [ ] Test with many small video files
- [ ] Monitor memory usage during processing
- [ ] Monitor CPU usage during processing

### Scalability Testing
- [ ] Backend service scales horizontally
- [ ] Database performance under load
- [ ] Redis performance under load
- [ ] Google Cloud services handle load

## Security Testing

### Authentication and Authorization
- [ ] Unauthenticated requests are rejected
- [ ] Users can only access their own jobs
- [ ] JWT token validation works correctly
- [ ] Token refresh works if implemented

### Data Protection
- [ ] Temporary files are cleaned up
- [ ] Cloud Storage access is properly secured
- [ ] Service account permissions are minimal
- [ ] No sensitive data in logs

## Monitoring and Logging

### Application Logging
- [ ] Compression job lifecycle is logged
- [ ] Errors are logged with sufficient detail
- [ ] Performance metrics are logged
- [ ] No sensitive data in logs

### Metrics and Alerts
- [ ] Job success/failure rates
- [ ] Processing times
- [ ] Cloud Storage usage
- [ ] Transcoder API usage and costs
- [ ] Error rates and types

## Deployment Testing

### Local Development
- [ ] Application runs locally with Docker Compose
- [ ] Database migrations work
- [ ] All services communicate correctly
- [ ] Environment variables are properly configured

### Staging Environment
- [ ] Helm chart deploys successfully
- [ ] All pods start and become ready
- [ ] Services are accessible
- [ ] Secrets are properly mounted
- [ ] Environment variables are correct

### Production Readiness
- [ ] Health checks work
- [ ] Graceful shutdown works
- [ ] Resource limits are appropriate
- [ ] Backup and recovery procedures tested

## Rollback Testing

### Rollback Scenarios
- [ ] Database migration rollback
- [ ] Application rollback to previous version
- [ ] Configuration rollback
- [ ] Verify old worker service can be restored if needed

## Cost and Performance Validation

### Cost Analysis
- [ ] Google Transcoder API costs per job
- [ ] Cloud Storage costs
- [ ] Compare costs to previous FFmpeg solution
- [ ] Verify cost optimization features work

### Performance Comparison
- [ ] Compare processing times to FFmpeg
- [ ] Compare compression quality
- [ ] Compare resource usage
- [ ] Verify scalability improvements

## Documentation and Training

### Documentation Updates
- [ ] API documentation updated
- [ ] Deployment guide updated
- [ ] Troubleshooting guide created
- [ ] Migration guide completed

### Team Training
- [ ] Development team trained on new architecture
- [ ] Operations team trained on monitoring and troubleshooting
- [ ] Support team trained on new features

## Sign-off Criteria

### Technical Sign-off
- [ ] All critical tests pass
- [ ] Performance meets requirements
- [ ] Security review completed
- [ ] Code review completed

### Business Sign-off
- [ ] User acceptance testing completed
- [ ] Cost analysis approved
- [ ] Risk assessment completed
- [ ] Go-live plan approved
