// Mock localStorage for testing
export {};

const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    }
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

test('localStorage persistence for UI states', () => {
  // Test that localStorage keys are properly set with default values
  const getInitialBooleanState = (key: string, defaultValue: boolean): boolean => {
    try {
      const stored = localStorage.getItem(key);
      if (stored !== null) {
        return stored === 'true';
      }
    } catch (error) {
      console.warn(`Failed to read ${key} from localStorage:`, error);
    }
    return defaultValue;
  };

  // Test default values
  expect(getInitialBooleanState('operations-sidebar-collapsed', false)).toBe(false);
  expect(getInitialBooleanState('show-instructions', true)).toBe(true);
  expect(getInitialBooleanState('jobs-panel-collapsed', true)).toBe(true);

  // Test setting values
  localStorage.setItem('operations-sidebar-collapsed', 'true');
  localStorage.setItem('show-instructions', 'false');
  localStorage.setItem('jobs-panel-collapsed', 'false');

  expect(getInitialBooleanState('operations-sidebar-collapsed', false)).toBe(true);
  expect(getInitialBooleanState('show-instructions', true)).toBe(false);
  expect(getInitialBooleanState('jobs-panel-collapsed', true)).toBe(false);
});
