import React from 'react';
import {
  Box,
  LinearProgress,
  Typography,
  Chip,
  Fade,
  CircularProgress
} from '@mui/material';
import {
  CheckCircle,
  Error,
  Cancel,
  PlayArrow
} from '@mui/icons-material';

export interface CompressionStatus {
  jobId?: string;
  status: string;
  message: string;
  progress: number;
  error?: string;
}

interface CompressionStatusIndicatorProps {
  mediaItemId: string;
  status?: CompressionStatus;
  compact?: boolean;
  showOnlyLoadingCircle?: boolean; // New prop to show only loading circle for jobs panel
}

const CompressionStatusIndicator: React.FC<CompressionStatusIndicatorProps> = ({
  mediaItemId,
  status,
  compact = false,
  showOnlyLoadingCircle = false
}) => {
  if (!status) {
    return null;
  }

  const getStatusColor = (statusStr: string) => {
    switch (statusStr.toLowerCase()) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'cancelled':
        return 'default';
      case 'queued':
      case 'downloadingfromgooglephotos':
      case 'uploadingtostorage':
      case 'transcodinginprogress':
      case 'compressingimage':
      case 'downloadingfromstorage':
      case 'readyforbatchupload':
      case 'uploadingtogooglephotos':
      case 'deletingoriginal':
        return 'primary';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (statusStr: string) => {
    switch (statusStr.toLowerCase()) {
      case 'completed':
        return <CheckCircle sx={{ fontSize: 16 }} />;
      case 'failed':
        return <Error sx={{ fontSize: 16 }} />;
      case 'cancelled':
        return <Cancel sx={{ fontSize: 16 }} />;
      default:
        return <PlayArrow sx={{ fontSize: 16 }} />;
    }
  };

  const isProcessing = !['completed', 'failed', 'cancelled'].includes(status.status.toLowerCase());

  // If showOnlyLoadingCircle is true and job is processing, show only a small loading circle
  if (showOnlyLoadingCircle && isProcessing) {
    return (
      <Fade in={true}>
        <Box
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            zIndex: 2
          }}
        >
          <CircularProgress
            size={20}
            sx={{
              color: 'primary.main',
              backgroundColor: (theme) =>
                theme.palette.mode === 'dark'
                  ? 'rgba(0, 0, 0, 0.9)'
                  : 'rgba(255, 255, 255, 0.9)',
              borderRadius: '50%',
              p: 0.5
            }}
          />
        </Box>
      </Fade>
    );
  }

  // If showOnlyLoadingCircle is true but job is not processing, show nothing (details are in jobs panel)
  if (showOnlyLoadingCircle) {
    return null;
  }

  if (compact) {
    return (
      <Fade in={true}>
        <Box
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            zIndex: 2
          }}
        >
          <Chip
            icon={isProcessing ? <CircularProgress size={12} /> : getStatusIcon(status.status)}
            label={status.message}
            size="small"
            color={getStatusColor(status.status) as any}
            sx={{
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(4px)',
              fontSize: '10px',
              height: 24,
              '& .MuiChip-icon': {
                fontSize: 12
              }
            }}
          />
        </Box>
      </Fade>
    );
  }

  return (
    <Fade in={true}>
      <Box
        sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          background: 'linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0.4), transparent)',
          color: 'white',
          p: 1,
          zIndex: 2
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
          {isProcessing ? (
            <CircularProgress size={16} sx={{ color: 'white' }} />
          ) : (
            getStatusIcon(status.status)
          )}
          <Typography variant="caption" sx={{ fontSize: '11px', fontWeight: 500 }}>
            {status.message}
          </Typography>
        </Box>
        
        {isProcessing && (
          <LinearProgress
            variant="determinate"
            value={status.progress}
            sx={{
              height: 3,
              borderRadius: 1.5,
              backgroundColor: 'rgba(255, 255, 255, 0.3)',
              '& .MuiLinearProgress-bar': {
                backgroundColor: 'white',
                borderRadius: 1.5
              }
            }}
          />
        )}
        
        {status.error && (
          <Typography variant="caption" sx={{ 
            fontSize: '10px', 
            color: 'error.light',
            display: 'block',
            mt: 0.5
          }}>
            {status.error}
          </Typography>
        )}
      </Box>
    </Fade>
  );
};

export default CompressionStatusIndicator;
