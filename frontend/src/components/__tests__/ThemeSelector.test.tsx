import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import ThemeSelector from '../ThemeSelector';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { lightTheme } from '../../theme/themes';

// Mock the useTheme hook for testing
const mockSetThemeMode = jest.fn();
const mockThemeContext = {
  themeMode: 'system' as const,
  effectiveTheme: 'light' as const,
  setThemeMode: mockSetThemeMode,
};

jest.mock('../../contexts/ThemeContext', () => ({
  ...jest.requireActual('../../contexts/ThemeContext'),
  useTheme: () => mockThemeContext,
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider>
    <MuiThemeProvider theme={lightTheme}>
      {children}
    </MuiThemeProvider>
  </ThemeProvider>
);

describe('ThemeSelector', () => {
  beforeEach(() => {
    mockSetThemeMode.mockClear();
  });

  it('renders theme selector button', () => {
    render(
      <TestWrapper>
        <ThemeSelector />
      </TestWrapper>
    );

    const themeButton = screen.getByRole('button', { name: /change theme/i });
    expect(themeButton).toBeInTheDocument();
  });

  it('opens theme menu when clicked', () => {
    render(
      <TestWrapper>
        <ThemeSelector />
      </TestWrapper>
    );

    const themeButton = screen.getByRole('button', { name: /change theme/i });
    fireEvent.click(themeButton);

    expect(screen.getByText('Theme')).toBeInTheDocument();
    expect(screen.getByText('Light')).toBeInTheDocument();
    expect(screen.getByText('Dark')).toBeInTheDocument();
    expect(screen.getByText('System')).toBeInTheDocument();
  });

  it('calls setThemeMode when theme option is selected', () => {
    render(
      <TestWrapper>
        <ThemeSelector />
      </TestWrapper>
    );

    const themeButton = screen.getByRole('button', { name: /change theme/i });
    fireEvent.click(themeButton);

    const darkOption = screen.getByText('Dark');
    fireEvent.click(darkOption);

    expect(mockSetThemeMode).toHaveBeenCalledWith('dark');
  });
});
