import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import Theme<PERSON>wareLogo from '../ThemeAwareLogo';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { lightTheme } from '../../theme/themes';

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock the useTheme hook for testing
const mockThemeContext = {
  themeMode: 'system' as const,
  effectiveTheme: 'light' as const,
  setThemeMode: jest.fn(),
};

jest.mock('../../contexts/ThemeContext', () => ({
  ...jest.requireActual('../../contexts/ThemeContext'),
  useTheme: () => mockThemeContext,
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <MuiThemeProvider theme={lightTheme}>
    <ThemeProvider>
      {children}
    </ThemeProvider>
  </MuiThemeProvider>
);

describe('ThemeAwareLogo', () => {
  it('renders light mode logo when theme is light', () => {
    mockThemeContext.effectiveTheme = 'light';
    
    render(
      <TestWrapper>
        <ThemeAwareLogo />
      </TestWrapper>
    );

    const logo = screen.getByAltText('Gallery Tuner');
    expect(logo).toBeInTheDocument();
    expect(logo).toHaveAttribute('src', '/gallery_tuner_light_mode.png');
  });

  it('renders dark mode logo when theme is dark', () => {
    mockThemeContext.effectiveTheme = 'dark';
    
    render(
      <TestWrapper>
        <ThemeAwareLogo />
      </TestWrapper>
    );

    const logo = screen.getByAltText('Gallery Tuner');
    expect(logo).toBeInTheDocument();
    expect(logo).toHaveAttribute('src', '/gallery_tuner_dark_mode.png');
  });

  it('applies custom size prop', () => {
    render(
      <TestWrapper>
        <ThemeAwareLogo size={48} />
      </TestWrapper>
    );

    const logo = screen.getByAltText('Gallery Tuner');
    expect(logo).toHaveStyle({ width: '48px', height: '48px' });
  });

  it('applies custom alt text', () => {
    render(
      <TestWrapper>
        <ThemeAwareLogo alt="Custom Alt Text" />
      </TestWrapper>
    );

    const logo = screen.getByAltText('Custom Alt Text');
    expect(logo).toBeInTheDocument();
  });

  it('applies custom sx styles', () => {
    render(
      <TestWrapper>
        <ThemeAwareLogo sx={{ borderRadius: '50%' }} />
      </TestWrapper>
    );

    const logo = screen.getByAltText('Gallery Tuner');
    expect(logo).toHaveStyle({ borderRadius: '50%' });
  });
});
