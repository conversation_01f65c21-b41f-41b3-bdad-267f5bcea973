import React, { useEffect, useRef } from 'react';

interface GoogleOneTapProps {
  onSuccess: (token: string) => void;
  onError?: (error: any) => void;
  disabled?: boolean;
  showDebugButton?: boolean;
}

declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void;
          prompt: (callback?: (notification: any) => void) => void;
          cancel: () => void;
          disableAutoSelect: () => void;
        };
      };
    };
  }
}

const GoogleOneTap: React.FC<GoogleOneTapProps> = ({
  onSuccess,
  onError,
  disabled = false,
  showDebugButton = false
}) => {
  const initialized = useRef(false);
  const clientIdRef = useRef<string | null>(null);
  const promptInProgress = useRef(false);

  console.log('GoogleOneTap component mounted, disabled:', disabled);

  useEffect(() => {
    const initializeOneTap = async () => {
      // Don't initialize if disabled or already initialized
      if (disabled || initialized.current) {
        console.log('GoogleOneTap: Skipping initialization - disabled:', disabled, 'initialized:', initialized.current);
        return;
      }

      console.log('GoogleOneTap: Starting initialization...');

      try {
        // Fetch Google Client ID from backend
        if (!clientIdRef.current) {
          const response = await fetch('/api/auth/google-client-id');
          if (!response.ok) {
            throw new Error('Failed to fetch Google Client ID');
          }
          const data = await response.json();
          clientIdRef.current = data.clientId;
        }

        // Wait for Google Identity Services to load
        if (!window.google?.accounts?.id) {
          console.log('Google Identity Services not yet loaded, waiting...');
          // Retry with exponential backoff, max 10 seconds
          const retryCount = (initializeOneTap as any).retryCount || 0;
          if (retryCount < 50) { // 50 * 200ms = 10 seconds max
            (initializeOneTap as any).retryCount = retryCount + 1;
            setTimeout(initializeOneTap, 200);
          } else {
            console.warn('Google Identity Services failed to load after 10 seconds');
          }
          return;
        }

        console.log('Initializing Google One Tap with client ID:', clientIdRef.current);
        console.log('Current origin:', window.location.origin);
        console.log('Google Identity Services available:', !!window.google?.accounts?.id);

        // Initialize Google One Tap
        window.google.accounts.id.initialize({
          client_id: clientIdRef.current,
          callback: handleCredentialResponse,
          auto_select: false,
          cancel_on_tap_outside: true,
          context: 'signin',
          ux_mode: 'popup',
          use_fedcm_for_prompt: true  // Enable FedCM for future compatibility
        });

        console.log('Google One Tap initialized, showing prompt...');

        // Show the One Tap prompt
        // With FedCM enabled, we call prompt without callback to avoid deprecated method warnings
        // Add protection against duplicate prompt calls
        if (!promptInProgress.current) {
          promptInProgress.current = true;
          try {
            window.google.accounts.id.prompt();
          } catch (error) {
            console.error('Error showing One Tap prompt:', error);
            promptInProgress.current = false;
          }
        } else {
          console.log('One Tap prompt already in progress, skipping duplicate call');
        }

        initialized.current = true;
      } catch (error) {
        console.error('Error initializing Google One Tap:', error);
        if (onError) {
          onError(error);
        }
      }
    };

    const handleCredentialResponse = async (response: any) => {
      console.log('Google One Tap credential response received');

      try {
        if (!response.credential) {
          throw new Error('No credential received from Google One Tap');
        }

        // Send the ID token to our backend
        const authResponse = await fetch('/api/auth/one-tap-signin', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            idToken: response.credential
          })
        });

        if (!authResponse.ok) {
          const errorText = await authResponse.text();
          throw new Error(`Authentication failed: ${authResponse.status} - ${errorText}`);
        }

        const authData = await authResponse.json();
        console.log('One Tap authentication successful');

        // Store the JWT token and call success callback
        localStorage.setItem('jwt', authData.token);
        onSuccess(authData.token);

      } catch (error) {
        console.error('Error processing One Tap credential:', error);
        if (onError) {
          onError(error);
        }
      } finally {
        // Reset prompt progress flag after credential processing
        promptInProgress.current = false;
      }
    };

    // Initialize One Tap when component mounts
    initializeOneTap();

    // Cleanup function
    return () => {
      if (window.google?.accounts?.id && initialized.current) {
        try {
          window.google.accounts.id.cancel();
          promptInProgress.current = false;
        } catch (error) {
          console.log('Error canceling One Tap:', error);
        }
      }
    };
  }, [disabled, onSuccess, onError]);

  // Cancel One Tap when component becomes disabled
  useEffect(() => {
    if (disabled && window.google?.accounts?.id && initialized.current) {
      try {
        window.google.accounts.id.cancel();
      } catch (error) {
        console.log('Error canceling One Tap on disable:', error);
      }
    }
  }, [disabled]);

  const manualTrigger = () => {
    if (window.google?.accounts?.id && clientIdRef.current) {
      console.log('Manually triggering One Tap prompt...');
      // With FedCM enabled, we call prompt without callback to avoid deprecated method warnings
      // Add protection against duplicate prompt calls
      if (!promptInProgress.current) {
        promptInProgress.current = true;
        try {
          window.google.accounts.id.prompt();
        } catch (error) {
          console.error('Error manually triggering One Tap prompt:', error);
          promptInProgress.current = false;
        }
      } else {
        console.log('Manual trigger: One Tap prompt already in progress, skipping duplicate call');
      }
    } else {
      console.log('Cannot manually trigger - Google not loaded or no client ID');
    }
  };

  // This component doesn't render anything visible by default
  // But can show a debug button for testing
  if (showDebugButton) {
    return (
      <div style={{ position: 'fixed', top: '10px', right: '10px', zIndex: 9999 }}>
        <button onClick={manualTrigger} style={{ padding: '10px', backgroundColor: '#4285f4', color: 'white', border: 'none', borderRadius: '4px' }}>
          Trigger One Tap
        </button>
      </div>
    );
  }

  return null;
};

export default GoogleOneTap;
