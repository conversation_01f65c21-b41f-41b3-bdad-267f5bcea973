import React from 'react';
import { But<PERSON>, Box } from '@mui/material';

interface GoogleSignInButtonProps {
  onSuccess: () => void;
  onError?: (error: any) => void;
  size?: 'large' | 'medium' | 'small';
  theme?: 'outline' | 'filled_blue' | 'filled_black';
  text?: 'signin_with' | 'signup_with' | 'continue_with' | 'signin';
  shape?: 'rectangular' | 'pill' | 'circle' | 'square';
  width?: number;
}

const GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({
  onSuccess,
  onError,
  size = 'large',
  theme = 'outline',
  text = 'signin_with',
  shape = 'rectangular',
  width = 300
}) => {
  const handleClick = () => {
    try {
      onSuccess();
    } catch (error) {
      console.error('Error during sign-in:', error);
      if (onError) {
        onError(error);
      }
    }
  };

  // Convert size prop to MUI button size
  const buttonSize = size === 'large' ? 'large' : size === 'small' ? 'small' : 'medium';

  // Convert theme to MUI button variant
  const variant = theme === 'filled_blue' || theme === 'filled_black' ? 'contained' : 'outlined';

  // Convert text prop to display text
  const getButtonText = () => {
    switch (text) {
      case 'signin_with':
        return 'Sign in with Google';
      case 'signup_with':
        return 'Sign up with Google';
      case 'continue_with':
        return 'Continue with Google';
      case 'signin':
        return 'Sign in';
      default:
        return 'Sign in with Google';
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Button
        variant={variant}
        size={buttonSize}
        onClick={handleClick}
        sx={{
          width: width,
          borderRadius: shape === 'pill' ? 25 : shape === 'circle' ? '50%' : 1,
          textTransform: 'none',
          fontWeight: 500,
          backgroundColor: theme === 'filled_blue' ? '#1976d2' : theme === 'filled_black' ? '#000' : 'transparent',
          color: theme === 'filled_blue' ? 'white' : theme === 'filled_black' ? 'white' : '#1976d2',
          borderColor: '#1976d2',
          '&:hover': {
            backgroundColor: theme === 'filled_blue' ? '#1565c0' : theme === 'filled_black' ? '#333' : 'rgba(25, 118, 210, 0.04)',
          },
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          px: 2,
          py: 1
        }}
        startIcon={
          <img
            src="https://developers.google.com/identity/images/g-logo.png"
            alt="Google"
            style={{ width: 18, height: 18 }}
          />
        }
      >
        {getButtonText()}
      </Button>
    </Box>
  );
};

export default GoogleSignInButton;
