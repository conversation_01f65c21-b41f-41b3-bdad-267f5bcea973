import React from 'react';
import { Button } from '@mui/material';
import { Star } from '@mui/icons-material';
import { loadStripe } from '@stripe/stripe-js';

let stripePromise: Promise<import('@stripe/stripe-js').Stripe | null> | null = null;
async function getStripe() {
  if (!stripePromise) {
    try {
      const res = await fetch('/api/stripe/publishable-key');
      if (!res.ok) throw new Error('Failed to fetch Stripe publishable key');
      const data = await res.json();
      stripePromise = loadStripe(data.publishableKey);
    } catch (e) {
      console.error(e);
      stripePromise = Promise.resolve(null);
    }
  }
  return stripePromise;
}

const Subscribe: React.FC = () => {
  const handleClick = async () => {
    const stripe = await getStripe();

    const token = localStorage.getItem('jwt');
    const response = await fetch('/api/payments/create-checkout-session', {
      method: 'POST',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    });

    const session = await response.json();

    if (stripe) {
      const result = await stripe.redirectToCheckout({
        sessionId: session.id,
      });

      if (result.error) {
        console.error(result.error.message);
      }
    }
  };

  return (
    <Button
      variant="contained"
      startIcon={<Star />}
      onClick={handleClick}
      sx={{
        backgroundColor: 'warning.main',
        color: 'warning.contrastText',
        textTransform: 'none',
        fontWeight: 500,
        boxShadow: 'none',
        '&:hover': {
          backgroundColor: 'warning.dark',
          boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)'
        },
        borderRadius: 1,
        px: 3,
        py: 1.5
      }}
    >
      Subscribe to Premium
    </Button>
  );
};

export default Subscribe;
