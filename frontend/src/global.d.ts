// src/global.d.ts
export {};

declare global {
  interface Window {
    termly?: {
      displayPreferences: () => void;
    };
    // Newer Termly embed API exposes a capitalized Termly object
    Termly?: {
      initialize: () => void;
      on: (event: string, handler: (...args: any[]) => void) => void;
      off: (event: string, handler: (...args: any[]) => void) => void;
      getConsentState: () => any;
    };
  }
}