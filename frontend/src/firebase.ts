import { initializeApp, getApp, getApps } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';

// Firebase configuration - ensure these are correct
const firebaseConfig = {
  apiKey: process.env.REACT_APP_GOOGLE_API_KEY,
  projectId: "tranquil-bison-465923-v9"
};

// Initialize Firebase App safely for environments with hot-reloading
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();

// Get the Firestore instance for the SPECIFIC database ID
const db = getFirestore(app, 'gallerytuner');

// Connect to Firestore emulator in development
if (process.env.NODE_ENV === 'development' && process.env.REACT_APP_USE_FIRESTORE_EMULATOR === 'true') {
  console.log('Connecting to Firestore emulator at localhost:8080...');
  connectFirestoreEmulator(db, 'localhost', 8080);
} else {
  console.log('Using production Firestore database: gallerytuner');
}

export { db };