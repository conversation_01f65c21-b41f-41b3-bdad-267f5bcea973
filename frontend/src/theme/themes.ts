import { createTheme, Theme } from '@mui/material/styles';

// Common typography configuration
const commonTypography = {
  fontFamily: [
    'Poppins',
    '-apple-system',
    'BlinkMacSystemFont',
    '"Segoe UI"',
    'Roboto',
    '"Helvetica Neue"',
    'Arial',
    'sans-serif'
  ].join(','),
  h1: {
    fontFamily: 'Poppins, sans-serif',
  },
  h2: {
    fontFamily: 'Poppins, sans-serif',
  },
  h3: {
    fontFamily: 'Poppins, sans-serif',
  },
  h4: {
    fontFamily: 'Poppins, sans-serif',
  },
  h5: {
    fontFamily: 'Poppins, sans-serif',
  },
  h6: {
    fontFamily: 'Poppins, sans-serif',
  },
  subtitle1: {
    fontFamily: 'Poppins, sans-serif',
  },
  subtitle2: {
    fontFamily: 'Poppins, sans-serif',
  },
  body1: {
    fontFamily: 'Poppins, sans-serif',
  },
  body2: {
    fontFamily: 'Poppins, sans-serif',
  },
  button: {
    fontFamily: 'Poppins, sans-serif',
  },
  caption: {
    fontFamily: 'Poppins, sans-serif',
  },
  overline: {
    fontFamily: 'Poppins, sans-serif',
  },
};

// Light theme configuration
export const lightTheme: Theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1a73e8',
      light: '#4285f4',
      dark: '#1557b0',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#34a853',
      light: '#5bb974',
      dark: '#137333',
      contrastText: '#ffffff',
    },
    error: {
      main: '#ea4335',
      light: '#ff6659',
      dark: '#d33b2c',
      contrastText: '#ffffff',
    },
    warning: {
      main: '#fbbc04',
      light: '#fef7e0',
      dark: '#ea8600',
      contrastText: '#000000',
    },
    info: {
      main: '#4285f4',
      light: '#70a7ff',
      dark: '#1a73e8',
      contrastText: '#ffffff',
    },
    success: {
      main: '#34a853',
      light: '#5bb974',
      dark: '#137333',
      contrastText: '#ffffff',
    },
    background: {
      default: '#ffffff',
      paper: '#ffffff',
    },
    text: {
      primary: '#202124',
      secondary: '#5f6368',
      disabled: '#9aa0a6',
    },
    divider: '#e8eaed',
    grey: {
      50: '#f8f9fa',
      100: '#f1f3f4',
      200: '#e8eaed',
      300: '#dadce0',
      400: '#bdc1c6',
      500: '#9aa0a6',
      600: '#80868b',
      700: '#5f6368',
      800: '#3c4043',
      900: '#202124',
    },
  },
  typography: commonTypography,
  components: {
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#ffffff',
          color: '#202124',
          borderBottom: '1px solid #e8eaed',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundColor: '#ffffff',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 4,
        },
      },
    },
  },
});

// Dark theme configuration
export const darkTheme: Theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#4285f4',
      light: 'rgba(66, 133, 244, 0.12)',
      dark: '#1a73e8',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#5bb974',
      light: '#81c995',
      dark: '#34a853',
      contrastText: '#000000',
    },
    error: {
      main: '#ff6659',
      light: '#ff8a80',
      dark: '#ea4335',
      contrastText: '#ffffff',
    },
    warning: {
      main: '#fdd663',
      light: 'rgba(253, 214, 99, 0.12)',
      dark: '#fbbc04',
      contrastText: '#000000',
    },
    info: {
      main: '#70a7ff',
      light: '#9fc5ff',
      dark: '#4285f4',
      contrastText: '#ffffff',
    },
    success: {
      main: '#5bb974',
      light: 'rgba(91, 185, 116, 0.12)',
      dark: '#34a853',
      contrastText: '#000000',
    },
    background: {
      default: '#121212',
      paper: '#1e1e1e',
    },
    text: {
      primary: '#e8eaed',
      secondary: '#9aa0a6',
      disabled: '#5f6368',
    },
    divider: '#3c4043',
    grey: {
      50: '#2a2a2a',
      100: '#333333',
      200: '#3c4043',
      300: '#5f6368',
      400: '#80868b',
      500: '#9aa0a6',
      600: '#bdc1c6',
      700: '#dadce0',
      800: '#e8eaed',
      900: '#f8f9fa',
    },
  },
  typography: commonTypography,
  components: {
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#1e1e1e',
          color: '#e8eaed',
          borderBottom: '1px solid #3c4043',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundColor: '#1e1e1e',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 4,
        },
      },
    },
  },
});

// Function to get theme based on mode
export const getTheme = (mode: 'light' | 'dark'): Theme => {
  return mode === 'dark' ? darkTheme : lightTheme;
};
