#!/bin/bash

# VidCompressor Terraform Deployment Script
# This script helps you deploy different scenarios easily

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [SCENARIO] [ACTION]"
    echo ""
    echo "SCENARIOS:"
    echo "  dev-local      - Local development (~\$1-5/month)"
    echo "  staging-hybrid - Hybrid deployment (~\$11-20/month)"
    echo "  prod-cloud     - Full cloud deployment (~\$11-45/month)"
    echo "  custom         - Use existing terraform.tfvars"
    echo ""
    echo "ACTIONS:"
    echo "  plan           - Show what will be created/changed"
    echo "  apply          - Deploy the infrastructure"
    echo "  destroy        - Destroy the infrastructure"
    echo "  init           - Initialize Terraform"
    echo ""
    echo "Examples:"
    echo "  $0 dev-local plan"
    echo "  $0 staging-hybrid apply"
    echo "  $0 custom destroy"
}

# Check if Terraform is installed
check_terraform() {
    if ! command -v terraform &> /dev/null; then
        print_error "Terraform is not installed. Please install it first."
        exit 1
    fi
}

# Check if gcloud is authenticated
check_gcloud() {
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        print_warning "No active gcloud authentication found."
        print_status "Please run: gcloud auth application-default login"
        exit 1
    fi
}

# Setup modular configuration
setup_modular() {
    print_status "Setting up modular Terraform configuration..."
    
    # Backup existing files if they exist
    if [ -f "main.tf" ] && [ ! -f "main-original.tf" ]; then
        cp main.tf main-original.tf
        print_status "Backed up existing main.tf to main-original.tf"
    fi
    
    if [ -f "variables.tf" ] && [ ! -f "variables-original.tf" ]; then
        cp variables.tf variables-original.tf
        print_status "Backed up existing variables.tf to variables-original.tf"
    fi
    
    # Use modular configuration
    cp main-modular.tf main.tf
    cp variables-modular.tf variables.tf
    
    print_success "Modular configuration is now active"
}

# Setup scenario
setup_scenario() {
    local scenario=$1
    
    case $scenario in
        dev-local)
            print_status "Setting up local development scenario..."
            cp scenarios/dev-local.tfvars terraform.tfvars
            ;;
        staging-hybrid)
            print_status "Setting up staging hybrid scenario..."
            cp scenarios/staging-hybrid.tfvars terraform.tfvars
            ;;
        prod-cloud)
            print_status "Setting up production cloud scenario..."
            cp scenarios/prod-full-cloud.tfvars terraform.tfvars
            ;;
        custom)
            print_status "Using existing terraform.tfvars..."
            if [ ! -f "terraform.tfvars" ]; then
                print_error "terraform.tfvars not found. Please create it or use a predefined scenario."
                exit 1
            fi
            ;;
        *)
            print_error "Unknown scenario: $scenario"
            show_usage
            exit 1
            ;;
    esac
    
    print_success "Scenario '$scenario' is configured"
}

# Main script
main() {
    local scenario=$1
    local action=$2
    
    # Check arguments
    if [ $# -lt 2 ]; then
        show_usage
        exit 1
    fi
    
    # Check prerequisites
    check_terraform
    check_gcloud
    
    # Setup configuration
    setup_modular
    setup_scenario $scenario
    
    # Execute action
    case $action in
        init)
            print_status "Initializing Terraform..."
            terraform init
            print_success "Terraform initialized"
            ;;
        plan)
            print_status "Planning Terraform deployment..."
            terraform plan
            ;;
        apply)
            print_status "Applying Terraform configuration..."
            print_warning "This will create real resources and may incur costs."
            read -p "Are you sure you want to continue? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                terraform apply
                print_success "Deployment completed!"
                print_status "Run 'terraform output' to see important information."
            else
                print_status "Deployment cancelled."
            fi
            ;;
        destroy)
            print_warning "This will destroy all resources created by Terraform."
            read -p "Are you sure you want to continue? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                terraform destroy
                print_success "Resources destroyed!"
            else
                print_status "Destruction cancelled."
            fi
            ;;
        *)
            print_error "Unknown action: $action"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
