# VidCompressor Terraform Infrastructure

This directory contains the infrastructure-as-code for the VidCompressor application with flexible deployment options.

## 🏗️ Architecture Options

### **Current Setup: Local Development + Cloud Services**
- ✅ **Local**: Frontend, Backend, Database (PostgreSQL)
- ☁️ **Cloud**: Storage, Transcoder API, Task Queue
- 💰 **Cost**: ~$1-5/month

### **Production Setup: Full Cloud Deployment**
- ☁️ **Cloud**: Everything (Frontend via CDN, Backend on Cloud Run, Managed Database)
- 💰 **Cost**: ~$11-45/month

## 📁 File Structure

```
terraform/
├── main.tf                    # Original monolithic config (legacy)
├── main-modular.tf           # New modular infrastructure
├── variables.tf              # Original variables
├── variables-modular.tf      # Modular variables
├── providers.tf              # Provider configurations
├── terraform.tfvars         # Your project configuration
├── terraform.tfvars.example # Example configuration
├── scenarios/               # Pre-configured deployment scenarios
│   ├── dev-local.tfvars     # Local development
│   ├── staging-hybrid.tfvars # Hybrid deployment
│   └── prod-full-cloud.tfvars # Full cloud deployment
├── modules/                 # Reusable infrastructure modules
│   ├── core/               # APIs, service accounts, IAM
│   ├── storage/            # Cloud Storage buckets
│   ├── tasks/              # Cloud Tasks queues
│   ├── secrets/            # Secret Manager
│   ├── database/           # Cloud SQL PostgreSQL
│   └── cloud-run/          # Cloud Run deployment
└── README.md               # This file
```

## 🚀 Quick Start

### **Option A: Use Pre-configured Scenarios (Recommended)**

```bash
# 1. Choose your deployment scenario
cp scenarios/dev-local.tfvars terraform.tfvars

# 2. Update project ID in terraform.tfvars
# 3. Deploy with modular configuration
cp main-modular.tf main.tf
cp variables-modular.tf variables.tf

# 4. Deploy
terraform init
terraform plan
terraform apply
```

### **Option B: Legacy Monolithic Setup**

```bash
# Use the original single-file configuration
# (main.tf is already set up for local development)
terraform init
terraform plan
terraform apply
```

### **Available Scenarios**

1. **dev-local.tfvars**: Local development (~$1-5/month)
2. **staging-hybrid.tfvars**: Hybrid deployment (~$11-20/month)
3. **prod-full-cloud.tfvars**: Full cloud deployment (~$11-45/month)

## 📋 Deployment Scenarios

### **Scenario 1: Local Development (Current)**
```hcl
# In main.tf - everything is active except:
# - Database section (commented out)
# - Cloud Run section (commented out)
# - Production secrets (commented out)
```

**What you get:**
- Cloud storage for video processing
- Google Transcoder API access
- Task queue for job management
- Service account key for local development

**Cost**: ~$1-5/month

### **Scenario 2: Hybrid Deployment**
```hcl
# Uncomment database section only
# Keep Cloud Run commented out
```

**What you get:**
- Everything from Scenario 1
- Managed PostgreSQL database
- Keep backend running locally

**Cost**: ~$11-20/month

### **Scenario 3: Full Production**
```hcl
# Uncomment all sections
```

**What you get:**
- Complete cloud deployment
- Serverless backend (Cloud Run)
- Managed database
- Auto-scaling and high availability

**Cost**: ~$11-45/month

## 🔧 Configuration

### **Required Variables** (terraform.tfvars)
```hcl
project_id = "your-gcp-project-id"
region     = "us-central1"
```

### **Optional Variables**
```hcl
# Database password (if using Cloud SQL)
database_password = "your-secure-password"

# Docker image (if using Cloud Run)
docker_image = "gcr.io/your-project/backend:latest"
```

## 📊 Cost Breakdown

| Component | Local Dev | Hybrid | Full Cloud |
|-----------|-----------|--------|------------|
| Storage | $1-2 | $1-2 | $1-2 |
| Cloud Tasks | $0-1 | $0-1 | $0-1 |
| Database | $0 (local) | $10-15 | $10-15 |
| Cloud Run | $0 (local) | $0 (local) | $0-20 |
| Secrets | $0.50 | $0.50 | $0.50 |
| **Total** | **$1-5** | **$11-20** | **$11-45** |

## 🔐 Security Notes

### **Local Development**
- Service account key is saved locally
- Database runs on localhost
- Minimal cloud permissions

### **Production**
- Service account keys stored in Secret Manager
- Database has restricted network access
- Cloud Run uses service account identity

## 📝 Common Commands

```bash
# Initialize Terraform
terraform init

# See what will be created/changed
terraform plan

# Apply changes
terraform apply

# Destroy everything (be careful!)
terraform destroy

# Format code
terraform fmt

# Validate configuration
terraform validate
```

## 🔄 Migration Path

### **Local → Hybrid**
1. Uncomment database section in `main.tf`
2. Run `terraform apply`
3. Update local app config to use Cloud SQL
4. Migrate data from local PostgreSQL

### **Hybrid → Full Cloud**
1. Build and push Docker image
2. Uncomment Cloud Run section in `main.tf`
3. Run `terraform apply`
4. Update DNS to point to Cloud Run URL

### **Full Cloud → Local** (Rollback)
1. Comment out Cloud Run and/or database sections
2. Run `terraform apply`
3. Update app config back to local settings

## 🆘 Troubleshooting

### **Common Issues**

1. **"API not enabled"**
   ```bash
   gcloud services enable storage.googleapis.com transcoder.googleapis.com
   ```

2. **"Insufficient permissions"**
   ```bash
   gcloud auth application-default login
   ```

3. **"Resource already exists"**
   ```bash
   terraform import google_storage_bucket.input_videos your-bucket-name
   ```

### **Getting Help**

- Check Terraform output messages
- Review Google Cloud Console for resource status
- Verify service account permissions
- Check application logs for configuration issues

## 🎯 Next Steps

1. **Deploy current setup**: `terraform apply`
2. **Test local development** with cloud services
3. **Monitor costs** in Google Cloud Console
4. **Plan production migration** when ready
5. **Set up CI/CD** for automated deployments
