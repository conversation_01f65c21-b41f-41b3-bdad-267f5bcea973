# VidCompressor Infrastructure - Modular Architecture
# This file uses modules for better organization and reusability

# =============================================================================
# LOCAL VARIABLES
# =============================================================================

locals {
  environment = var.environment
  
  # Feature flags for different deployment scenarios
  enable_database   = var.enable_database
  enable_cloud_run  = var.enable_cloud_run
  enable_secrets    = var.enable_secrets
  
  # Common tags
  common_labels = {
    project     = "vidcompressor"
    environment = local.environment
    managed_by  = "terraform"
  }
}

# =============================================================================
# CORE INFRASTRUCTURE (ALWAYS DEPLOYED)
# =============================================================================

module "core" {
  source = "./modules/core"
  
  project_id = var.project_id
  region     = var.region
  
  # Enable APIs based on what we're deploying
  enable_cloud_run_api   = local.enable_cloud_run
  enable_cloud_sql_api   = local.enable_database
  enable_cloud_build_api = local.enable_cloud_run
  
  # Create local key file for development
  create_local_key_file = var.create_local_key_file
}

module "storage" {
  source = "./modules/storage"
  
  project_id  = var.project_id
  region      = var.region
  environment = local.environment
  
  # Storage configuration
  lifecycle_age_days = var.storage_lifecycle_days
  enable_cors        = var.enable_storage_cors
  cors_origins       = var.storage_cors_origins
  storage_class      = var.storage_class
}

module "tasks" {
  source = "./modules/tasks"
  
  project_id  = var.project_id
  region      = var.region
  environment = local.environment
  
  # Task queue configuration
  max_concurrent_dispatches = var.task_max_concurrent_dispatches
  max_dispatches_per_second = var.task_max_dispatches_per_second
  max_attempts              = var.task_max_attempts
}

# =============================================================================
# OPTIONAL: SECRETS (FOR PRODUCTION)
# =============================================================================

module "secrets" {
  count  = local.enable_secrets ? 1 : 0
  source = "./modules/secrets"
  
  project_id  = var.project_id
  environment = local.environment
  
  # Only create secret versions if values are provided
  create_secret_versions = var.create_secret_versions
  google_client_id       = var.google_client_id
  google_client_secret   = var.google_client_secret
  jwt_secret            = var.jwt_secret
  service_account_key   = local.enable_secrets && !var.create_local_key_file ? module.core.service_account_key_file : ""
}

# =============================================================================
# OPTIONAL: DATABASE (FOR CLOUD DEPLOYMENT)
# =============================================================================

module "database" {
  count  = local.enable_database ? 1 : 0
  source = "./modules/database"
  
  project_id        = var.project_id
  region           = var.region
  database_password = var.database_password
}

# =============================================================================
# OPTIONAL: CLOUD RUN (FOR SERVERLESS DEPLOYMENT)
# =============================================================================

module "cloud_run" {
  count  = local.enable_cloud_run ? 1 : 0
  source = "./modules/cloud-run"
  
  project_id     = var.project_id
  region         = var.region
  environment    = local.environment
  service_name   = var.cloud_run_service_name
  
  # Service configuration
  service_account_email = module.core.service_account_email
  docker_image         = var.docker_image
  
  # Storage configuration
  input_bucket_name  = module.storage.input_bucket_name
  output_bucket_name = module.storage.output_bucket_name
  temp_bucket_name   = module.storage.temp_bucket_name
  
  # Task queue configuration
  compression_queue_name = module.tasks.compression_queue_name
  
  # Database configuration (if enabled)
  database_connection_string = local.enable_database ? module.database[0].connection_string : ""
  
  # Resource limits
  cpu_limit    = var.cloud_run_cpu_limit
  memory_limit = var.cloud_run_memory_limit
  min_instances = var.cloud_run_min_instances
  max_instances = var.cloud_run_max_instances
  
  # Access control
  allow_public_access = var.cloud_run_allow_public_access
  
  # Custom environment variables
  custom_environment_variables = var.cloud_run_custom_env_vars
  
  depends_on = [module.core]
}

# =============================================================================
# OUTPUTS
# =============================================================================

# Core outputs
output "service_account_email" {
  value       = module.core.service_account_email
  description = "Service account email for the application"
}

output "service_account_key_file" {
  value       = module.core.service_account_key_file
  description = "Path to the service account key file (if created)"
}

output "docker_image_base_url" {
  value       = module.core.docker_image_base_url
  description = "Base URL for Docker images"
}

# Storage outputs
output "storage_buckets" {
  value       = module.storage.all_bucket_names
  description = "All storage bucket names"
}

output "input_bucket_name" {
  value       = module.storage.input_bucket_name
  description = "Input bucket name"
}

output "output_bucket_name" {
  value       = module.storage.output_bucket_name
  description = "Output bucket name"
}

# Task queue outputs
output "task_queues" {
  value       = module.tasks.all_queue_names
  description = "All task queue names"
}

output "compression_queue_name" {
  value       = module.tasks.compression_queue_name
  description = "Main compression queue name"
}

# Database outputs (if enabled)
output "database_ip" {
  value       = local.enable_database ? module.database[0].database_ip : null
  description = "Database IP address (if database is enabled)"
}

output "database_connection_string" {
  value       = local.enable_database ? module.database[0].connection_string : null
  description = "Database connection string (if database is enabled)"
  sensitive   = true
}

# Cloud Run outputs (if enabled)
output "backend_url" {
  value       = local.enable_cloud_run ? module.cloud_run[0].backend_url : null
  description = "Backend URL (if Cloud Run is enabled)"
}

# Secrets outputs (if enabled)
output "secret_names" {
  value       = local.enable_secrets ? module.secrets[0].secret_names : null
  description = "Secret names (if secrets are enabled)"
}

# Configuration summary
output "deployment_summary" {
  value = {
    environment    = local.environment
    database       = local.enable_database ? "Cloud SQL" : "Local PostgreSQL"
    backend        = local.enable_cloud_run ? "Cloud Run" : "Local Development"
    secrets        = local.enable_secrets ? "Secret Manager" : "Local Configuration"
    estimated_cost = local.enable_cloud_run && local.enable_database ? "$11-45/month" : local.enable_database ? "$11-20/month" : "$1-5/month"
  }
  description = "Summary of the current deployment configuration"
}
