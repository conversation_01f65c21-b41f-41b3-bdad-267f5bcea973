# Secrets Module - Secret Manager for secure configuration
# This module manages application secrets and sensitive configuration

variable "project_id" {
  description = "Google Cloud project ID"
  type        = string
}

variable "environment" {
  description = "Environment (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "create_secret_versions" {
  description = "Whether to create secret versions with default values"
  type        = bool
  default     = false
}

variable "google_client_id" {
  description = "Google OAuth client ID"
  type        = string
  default     = ""
  sensitive   = true
}

variable "google_client_secret" {
  description = "Google OAuth client secret"
  type        = string
  default     = ""
  sensitive   = true
}

variable "jwt_secret" {
  description = "JWT signing secret"
  type        = string
  default     = ""
  sensitive   = true
}

variable "service_account_key" {
  description = "Service account private key (base64 decoded)"
  type        = string
  default     = ""
  sensitive   = true
}

variable "stripe_secret_key" {
  description = "Stripe secret key"
  type        = string
  default     = ""
  sensitive   = true
}

variable "stripe_webhook_secret" {
  description = "Stripe webhook secret"
  type        = string
  default     = ""
  sensitive   = true
}

# Google Client ID secret
resource "google_secret_manager_secret" "google_client_id" {
  secret_id = "google-client-id-${var.environment}"

  replication {
    auto {}
  }
  
  labels = {
    environment = var.environment
    purpose     = "oauth-client-id"
    application = "vidcompressor"
  }
}

# Google Client Secret
resource "google_secret_manager_secret" "google_client_secret" {
  secret_id = "google-client-secret-${var.environment}"

  replication {
    auto {}
  }
  
  labels = {
    environment = var.environment
    purpose     = "oauth-client-secret"
    application = "vidcompressor"
  }
}

# JWT Secret
resource "google_secret_manager_secret" "jwt_secret" {
  secret_id = "jwt-secret-${var.environment}"

  replication {
    auto {}
  }
  
  labels = {
    environment = var.environment
    purpose     = "jwt-signing"
    application = "vidcompressor"
  }
}

# Service Account Key (for production deployments)
resource "google_secret_manager_secret" "service_account_key" {
  secret_id = "service-account-key-${var.environment}"

  replication {
    auto {}
  }
  
  labels = {
    environment = var.environment
    purpose     = "service-account-auth"
    application = "vidcompressor"
  }
}

# Database Password (for Cloud SQL deployments)
resource "google_secret_manager_secret" "database_password" {
  secret_id = "database-password-${var.environment}"

  replication {
    auto {}
  }

  labels = {
    environment = var.environment
    purpose     = "database-auth"
    application = "vidcompressor"
  }
}

# Stripe Secret Key
resource "google_secret_manager_secret" "stripe_secret_key" {
  secret_id = "stripe-secret-key-${var.environment}"

  replication {
    auto {}
  }

  labels = {
    environment = var.environment
    purpose     = "payment-processing"
    application = "vidcompressor"
  }
}

# Stripe Webhook Secret
resource "google_secret_manager_secret" "stripe_webhook_secret" {
  secret_id = "stripe-webhook-secret-${var.environment}"

  replication {
    auto {}
  }

  labels = {
    environment = var.environment
    purpose     = "webhook-verification"
    application = "vidcompressor"
  }
}

# Optional: Create secret versions with provided values
resource "google_secret_manager_secret_version" "google_client_id_value" {
  count       = var.create_secret_versions && var.google_client_id != "" ? 1 : 0
  secret      = google_secret_manager_secret.google_client_id.id
  secret_data = var.google_client_id
}

resource "google_secret_manager_secret_version" "google_client_secret_value" {
  count       = var.create_secret_versions && var.google_client_secret != "" ? 1 : 0
  secret      = google_secret_manager_secret.google_client_secret.id
  secret_data = var.google_client_secret
}

resource "google_secret_manager_secret_version" "jwt_secret_value" {
  count       = var.create_secret_versions && var.jwt_secret != "" ? 1 : 0
  secret      = google_secret_manager_secret.jwt_secret.id
  secret_data = var.jwt_secret
}

resource "google_secret_manager_secret_version" "service_account_key_value" {
  count       = var.create_secret_versions && var.service_account_key != "" ? 1 : 0
  secret      = google_secret_manager_secret.service_account_key.id
  secret_data = var.service_account_key
}

resource "google_secret_manager_secret_version" "stripe_secret_key_value" {
  count       = var.create_secret_versions && var.stripe_secret_key != "" ? 1 : 0
  secret      = google_secret_manager_secret.stripe_secret_key.id
  secret_data = var.stripe_secret_key
}

resource "google_secret_manager_secret_version" "stripe_webhook_secret_value" {
  count       = var.create_secret_versions && var.stripe_webhook_secret != "" ? 1 : 0
  secret      = google_secret_manager_secret.stripe_webhook_secret.id
  secret_data = var.stripe_webhook_secret
}

# Outputs
output "secret_names" {
  value = {
    google_client_id      = google_secret_manager_secret.google_client_id.secret_id
    google_client_secret  = google_secret_manager_secret.google_client_secret.secret_id
    jwt_secret           = google_secret_manager_secret.jwt_secret.secret_id
    service_account_key  = google_secret_manager_secret.service_account_key.secret_id
    database_password    = google_secret_manager_secret.database_password.secret_id
    stripe_secret_key    = google_secret_manager_secret.stripe_secret_key.secret_id
    stripe_webhook_secret = google_secret_manager_secret.stripe_webhook_secret.secret_id
  }
  description = "Map of all secret names"
}

output "secret_ids" {
  value = {
    google_client_id      = google_secret_manager_secret.google_client_id.id
    google_client_secret  = google_secret_manager_secret.google_client_secret.id
    jwt_secret           = google_secret_manager_secret.jwt_secret.id
    service_account_key  = google_secret_manager_secret.service_account_key.id
    database_password    = google_secret_manager_secret.database_password.id
  }
  description = "Map of all secret IDs (full resource names)"
}

output "google_client_id_secret_name" {
  value       = google_secret_manager_secret.google_client_id.secret_id
  description = "Google Client ID secret name"
}

output "google_client_secret_secret_name" {
  value       = google_secret_manager_secret.google_client_secret.secret_id
  description = "Google Client Secret secret name"
}

output "jwt_secret_secret_name" {
  value       = google_secret_manager_secret.jwt_secret.secret_id
  description = "JWT secret name"
}

output "service_account_key_secret_name" {
  value       = google_secret_manager_secret.service_account_key.secret_id
  description = "Service account key secret name"
}

output "database_password_secret_name" {
  value       = google_secret_manager_secret.database_password.secret_id
  description = "Database password secret name"
}
