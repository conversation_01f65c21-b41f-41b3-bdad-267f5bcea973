# Tasks Module - Cloud Tasks queue for job processing
# This module creates the task queue infrastructure for background job processing

variable "project_id" {
  description = "Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "Google Cloud region"
  type        = string
}

variable "environment" {
  description = "Environment (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "queue_name" {
  description = "Name of the Cloud Tasks queue"
  type        = string
  default     = "video-compression-jobs"
}

variable "max_concurrent_dispatches" {
  description = "Maximum number of concurrent task dispatches"
  type        = number
  default     = 5
}

variable "max_dispatches_per_second" {
  description = "Maximum number of task dispatches per second"
  type        = number
  default     = 2
}

variable "max_attempts" {
  description = "Maximum number of retry attempts for failed tasks"
  type        = number
  default     = 3
}

variable "max_retry_duration" {
  description = "Maximum duration for retrying failed tasks"
  type        = string
  default     = "300s"
}

variable "min_backoff" {
  description = "Minimum backoff duration between retries"
  type        = string
  default     = "5s"
}

variable "max_backoff" {
  description = "Maximum backoff duration between retries"
  type        = string
  default     = "60s"
}

# Cloud Tasks queue for job processing
resource "google_cloud_tasks_queue" "compression_queue" {
  name     = "${var.queue_name}-${var.environment}"
  location = var.region

  rate_limits {
    max_concurrent_dispatches = var.max_concurrent_dispatches
    max_dispatches_per_second = var.max_dispatches_per_second
  }

  retry_config {
    max_attempts       = var.max_attempts
    max_retry_duration = var.max_retry_duration
    min_backoff        = var.min_backoff
    max_backoff        = var.max_backoff
  }
}

# Optional: High priority queue for urgent tasks
resource "google_cloud_tasks_queue" "priority_queue" {
  name     = "${var.queue_name}-priority-${var.environment}"
  location = var.region

  rate_limits {
    max_concurrent_dispatches = var.max_concurrent_dispatches * 2  # Higher throughput
    max_dispatches_per_second = var.max_dispatches_per_second * 2
  }

  retry_config {
    max_attempts       = var.max_attempts
    max_retry_duration = var.max_retry_duration
    min_backoff        = var.min_backoff
    max_backoff        = var.max_backoff
  }
}

# Optional: Dead letter queue for failed tasks
resource "google_cloud_tasks_queue" "dead_letter_queue" {
  name     = "${var.queue_name}-dlq-${var.environment}"
  location = var.region

  rate_limits {
    max_concurrent_dispatches = 1  # Slow processing for investigation
    max_dispatches_per_second = 0.1
  }

  retry_config {
    max_attempts       = 1  # No retries in DLQ
    max_retry_duration = "60s"
    min_backoff        = "10s"
    max_backoff        = "10s"
  }
}

# Outputs
output "compression_queue_name" {
  value       = google_cloud_tasks_queue.compression_queue.name
  description = "Name of the main compression queue"
}

output "compression_queue_id" {
  value       = google_cloud_tasks_queue.compression_queue.id
  description = "ID of the main compression queue"
}

output "priority_queue_name" {
  value       = google_cloud_tasks_queue.priority_queue.name
  description = "Name of the priority queue"
}

output "dead_letter_queue_name" {
  value       = google_cloud_tasks_queue.dead_letter_queue.name
  description = "Name of the dead letter queue"
}

output "all_queue_names" {
  value = {
    main     = google_cloud_tasks_queue.compression_queue.name
    priority = google_cloud_tasks_queue.priority_queue.name
    dlq      = google_cloud_tasks_queue.dead_letter_queue.name
  }
  description = "Map of all queue names"
}

output "queue_location" {
  value       = var.region
  description = "Location of the queues"
}
