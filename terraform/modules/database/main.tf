# Database Module - Cloud SQL PostgreSQL
# Use this module when you want to deploy a managed database instead of local PostgreSQL

variable "project_id" {
  description = "Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "Google Cloud region"
  type        = string
}

variable "database_password" {
  description = "Database password"
  type        = string
  sensitive   = true
  default     = "change-this-password"
}

# Cloud SQL PostgreSQL instance
resource "google_sql_database_instance" "postgres" {
  name             = "vidcompressor-db"
  database_version = "POSTGRES_15"
  region           = var.region

  settings {
    tier = "db-f1-micro"  # Cheapest option
    
    disk_type = "PD_HDD"  # Cheaper than SSD
    disk_size = 10        # Minimum size
    
    backup_configuration {
      enabled = true
      start_time = "03:00"
    }
    
    ip_configuration {
      ipv4_enabled = true
      authorized_networks {
        value = "0.0.0.0/0"  # Note: Restrict this in production
      }
    }
  }
  
  deletion_protection = false  # Allow deletion for dev/test
}

# Database
resource "google_sql_database" "database" {
  name     = "vidcompressor"
  instance = google_sql_database_instance.postgres.name
}

# Database user
resource "google_sql_user" "user" {
  name     = "vidcompressor"
  instance = google_sql_database_instance.postgres.name
  password = var.database_password
}

# Outputs
output "database_instance_name" {
  value = google_sql_database_instance.postgres.name
}

output "database_ip" {
  value = google_sql_database_instance.postgres.ip_address.0.ip_address
}

output "database_name" {
  value = google_sql_database.database.name
}

output "database_user" {
  value = google_sql_user.user.name
}

output "connection_string" {
  value = "Host=${google_sql_database_instance.postgres.ip_address.0.ip_address};Port=5432;Database=${google_sql_database.database.name};Username=${google_sql_user.user.name};Password=${var.database_password}"
  sensitive = true
}
