# Core Module - APIs, Service Accounts, and IAM
# This module sets up the foundational Google Cloud services and permissions

variable "project_id" {
  description = "Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "Google Cloud region"
  type        = string
}

variable "enable_cloud_run_api" {
  description = "Whether to enable Cloud Run API"
  type        = bool
  default     = false
}

variable "enable_cloud_sql_api" {
  description = "Whether to enable Cloud SQL API"
  type        = bool
  default     = false
}

variable "enable_cloud_build_api" {
  description = "Whether to enable Cloud Build API"
  type        = bool
  default     = false
}

variable "create_local_key_file" {
  description = "Whether to create a local service account key file for development"
  type        = bool
  default     = true
}

# Enable required APIs
resource "google_project_service" "required_apis" {
  for_each = toset(concat([
    "storage.googleapis.com",
    "transcoder.googleapis.com",
    "artifactregistry.googleapis.com",
    "cloudtasks.googleapis.com",
    "secretmanager.googleapis.com",
    "firestore.googleapis.com"
  ],
  var.enable_cloud_run_api ? ["run.googleapis.com"] : [],
  var.enable_cloud_sql_api ? ["sqladmin.googleapis.com"] : [],
  var.enable_cloud_build_api ? ["cloudbuild.googleapis.com"] : []
  ))

  service = each.value
  disable_on_destroy = false
}

# Artifact Registry for Docker images
resource "google_artifact_registry_repository" "registry" {
  location      = var.region
  repository_id = "vidcompressor-registry"
  format        = "DOCKER"
  description   = "Docker registry for VidCompressor application images"
  
  depends_on = [google_project_service.required_apis]
}

# Service account for the application
resource "google_service_account" "app_service_account" {
  account_id   = "vidcompressor-app"
  display_name = "VidCompressor Application Service Account"
  description  = "Service account for video compression application"
}

# IAM bindings for the service account
resource "google_project_iam_member" "transcoder_admin" {
  project = var.project_id
  role    = "roles/transcoder.admin"
  member  = "serviceAccount:${google_service_account.app_service_account.email}"
}

resource "google_project_iam_member" "storage_admin" {
  project = var.project_id
  role    = "roles/storage.admin"
  member  = "serviceAccount:${google_service_account.app_service_account.email}"
}

resource "google_project_iam_member" "cloudtasks_enqueuer" {
  project = var.project_id
  role    = "roles/cloudtasks.enqueuer"
  member  = "serviceAccount:${google_service_account.app_service_account.email}"
}

resource "google_project_iam_member" "cloudtasks_taskrunner" {
  project = var.project_id
  role    = "roles/cloudtasks.taskRunner"
  member  = "serviceAccount:${google_service_account.app_service_account.email}"
}

resource "google_project_iam_member" "firestore_user" {
  project = var.project_id
  role    = "roles/datastore.user"
  member  = "serviceAccount:${google_service_account.app_service_account.email}"
}

# Additional Firestore database user role for named databases
resource "google_project_iam_member" "firestore_database_user" {
  project = var.project_id
  role    = "roles/firebase.developAdmin"
  member  = "serviceAccount:${google_service_account.app_service_account.email}"
}

# Secret Manager access for Cloud Run services
resource "google_project_iam_member" "secret_accessor" {
  project = var.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.app_service_account.email}"
}

# Optional Cloud SQL IAM permission
resource "google_project_iam_member" "cloudsql_client" {
  count   = var.enable_cloud_sql_api ? 1 : 0
  project = var.project_id
  role    = "roles/cloudsql.client"
  member  = "serviceAccount:${google_service_account.app_service_account.email}"
}

# Service account key for local development (disabled due to org policy)
# Use gcloud auth application-default login instead
# resource "google_service_account_key" "app_key" {
#   count              = var.create_local_key_file ? 1 : 0
#   service_account_id = google_service_account.app_service_account.name
# }

# Save the service account key to a local file for development (disabled)
# resource "local_file" "service_account_key" {
#   count    = var.create_local_key_file ? 1 : 0
#   content  = base64decode(google_service_account_key.app_key[0].private_key)
#   filename = "${path.root}/service-account-key.json"
#
#   # Make sure the file is only readable by the owner
#   file_permission = "0600"
# }

# Outputs
output "service_account_email" {
  value       = google_service_account.app_service_account.email
  description = "Service account email for the application"
}

output "service_account_id" {
  value       = google_service_account.app_service_account.id
  description = "Service account ID"
}

output "service_account_key_file" {
  value       = null  # Disabled due to organization policy
  description = "Service account key creation disabled - use 'gcloud auth application-default login' instead"
}

output "artifact_registry_repository" {
  value       = google_artifact_registry_repository.registry.name
  description = "Artifact Registry repository name"
}

output "docker_image_base_url" {
  value       = "${var.region}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.registry.repository_id}"
  description = "Base URL for Docker images in Artifact Registry"
}
