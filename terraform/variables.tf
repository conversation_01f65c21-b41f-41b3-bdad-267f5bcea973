# Variables for Modular VidCompressor Infrastructure

# =============================================================================
# CORE VARIABLES
# =============================================================================

variable "project_id" {
  description = "Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "Google Cloud region"
  type        = string
  default     = "us-central1"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
  
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

# =============================================================================
# DEPLOYMENT FEATURE FLAGS
# =============================================================================

variable "enable_database" {
  description = "Whether to deploy Cloud SQL database"
  type        = bool
  default     = false
}

variable "enable_cloud_run" {
  description = "Whether to deploy Cloud Run service"
  type        = bool
  default     = false
}

variable "enable_secrets" {
  description = "Whether to create Secret Manager secrets"
  type        = bool
  default     = false
}

variable "create_local_key_file" {
  description = "Whether to create a local service account key file"
  type        = bool
  default     = true
}

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

variable "storage_lifecycle_days" {
  description = "Number of days after which files are automatically deleted"
  type        = number
  default     = 7
}

variable "enable_storage_cors" {
  description = "Whether to enable CORS for storage buckets"
  type        = bool
  default     = true
}

variable "storage_cors_origins" {
  description = "List of allowed CORS origins for storage buckets"
  type        = list(string)
  default     = ["http://localhost:3000", "http://localhost:5119"]
}

variable "storage_class" {
  description = "Storage class for buckets"
  type        = string
  default     = "STANDARD"
  
  validation {
    condition = contains([
      "STANDARD", 
      "NEARLINE", 
      "COLDLINE", 
      "ARCHIVE"
    ], var.storage_class)
    error_message = "Storage class must be one of: STANDARD, NEARLINE, COLDLINE, ARCHIVE."
  }
}

# =============================================================================
# TASK QUEUE CONFIGURATION
# =============================================================================

variable "task_max_concurrent_dispatches" {
  description = "Maximum number of concurrent task dispatches"
  type        = number
  default     = 5
}

variable "task_max_dispatches_per_second" {
  description = "Maximum number of task dispatches per second"
  type        = number
  default     = 2
}

variable "task_max_attempts" {
  description = "Maximum number of retry attempts for failed tasks"
  type        = number
  default     = 3
}

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

variable "database_password" {
  description = "Password for the database user"
  type        = string
  default     = "change-this-password"
  sensitive   = true
}

# =============================================================================
# CLOUD RUN CONFIGURATION
# =============================================================================

variable "cloud_run_service_name" {
  description = "Name of the Cloud Run service"
  type        = string
  default     = "vidcompressor-backend"
}

variable "docker_image" {
  description = "Docker image for the Cloud Run service"
  type        = string
  default     = "gcr.io/cloudrun/hello"  # Default placeholder image
}

variable "cloud_run_cpu_limit" {
  description = "CPU limit for Cloud Run service"
  type        = string
  default     = "1"
}

variable "cloud_run_memory_limit" {
  description = "Memory limit for Cloud Run service"
  type        = string
  default     = "1Gi"
}

variable "cloud_run_min_instances" {
  description = "Minimum number of Cloud Run instances"
  type        = number
  default     = 0
}

variable "cloud_run_max_instances" {
  description = "Maximum number of Cloud Run instances"
  type        = number
  default     = 10
}

variable "cloud_run_allow_public_access" {
  description = "Whether to allow public access to Cloud Run service"
  type        = bool
  default     = true
}

variable "cloud_run_custom_env_vars" {
  description = "Custom environment variables for Cloud Run service"
  type        = map(string)
  default     = {}
}

# =============================================================================
# SECRETS CONFIGURATION
# =============================================================================

variable "create_secret_versions" {
  description = "Whether to create secret versions with provided values"
  type        = bool
  default     = false
}

variable "google_client_id" {
  description = "Google OAuth client ID"
  type        = string
  default     = ""
  sensitive   = true
}

variable "google_client_secret" {
  description = "Google OAuth client secret"
  type        = string
  default     = ""
  sensitive   = true
}

variable "jwt_secret" {
  description = "JWT signing secret"
  type        = string
  default     = ""
  sensitive   = true
}
