# VidCompressor Modular Terraform Architecture

## 🎯 Overview

Your Terraform infrastructure has been completely modularized for better organization, reusability, and maintainability. You now have both the original monolithic setup and a new modular architecture.

## 📦 Module Structure

### **Core Modules**

| Module | Purpose | Resources |
|--------|---------|-----------|
| `core` | APIs, service accounts, IAM | 5-8 resources |
| `storage` | Cloud Storage buckets | 3 resources |
| `tasks` | Cloud Tasks queues | 3 resources |
| `secrets` | Secret Manager | 5 resources |
| `database` | Cloud SQL PostgreSQL | 3 resources |
| `cloud-run` | Serverless deployment | 2 resources |

### **Module Dependencies**

```mermaid
graph TD
    A[core] --> B[storage]
    A --> C[tasks]
    A --> D[secrets]
    A --> E[database]
    A --> F[cloud-run]
    B --> F
    C --> F
    D --> F
    E --> F
```

## 🚀 Deployment Options

### **Option 1: Quick Deploy with <PERSON>ript**

```bash
# Local development
./deploy.sh dev-local plan
./deploy.sh dev-local apply

# Staging hybrid
./deploy.sh staging-hybrid plan
./deploy.sh staging-hybrid apply

# Production cloud
./deploy.sh prod-cloud plan
./deploy.sh prod-cloud apply
```

### **Option 2: Manual Deployment**

```bash
# 1. Choose scenario
cp scenarios/dev-local.tfvars terraform.tfvars

# 2. Switch to modular config
cp main-modular.tf main.tf
cp variables-modular.tf variables.tf

# 3. Deploy
terraform init
terraform plan
terraform apply
```

### **Option 3: Custom Configuration**

Create your own `terraform.tfvars` with custom settings:

```hcl
project_id = "your-project"
environment = "custom"

# Enable only what you need
enable_database = true
enable_cloud_run = false
enable_secrets = true

# Custom storage settings
storage_lifecycle_days = 14
storage_class = "NEARLINE"

# Custom task queue settings
task_max_concurrent_dispatches = 8
```

## 📋 Deployment Scenarios

### **Scenario 1: Local Development**
```bash
./deploy.sh dev-local apply
```

**What you get:**
- ✅ Cloud Storage (video processing)
- ✅ Cloud Tasks (job queue)
- ✅ Service account (local key file)
- ✅ Artifact Registry (for future use)
- ❌ No database (use local PostgreSQL)
- ❌ No Cloud Run (run backend locally)
- ❌ No secrets (use local config)

**Cost:** ~$1-5/month

### **Scenario 2: Staging Hybrid**
```bash
./deploy.sh staging-hybrid apply
```

**What you get:**
- ✅ Everything from Scenario 1
- ✅ Cloud SQL database
- ✅ Secret Manager
- ❌ No Cloud Run (still run backend locally)

**Cost:** ~$11-20/month

### **Scenario 3: Production Cloud**
```bash
./deploy.sh prod-cloud apply
```

**What you get:**
- ✅ Everything from previous scenarios
- ✅ Cloud Run deployment
- ✅ Full production setup

**Cost:** ~$11-45/month

## 🔧 Module Configuration

### **Core Module**
```hcl
module "core" {
  source = "./modules/core"
  
  project_id = var.project_id
  region     = var.region
  
  enable_cloud_run_api   = var.enable_cloud_run
  enable_cloud_sql_api   = var.enable_database
  create_local_key_file  = var.create_local_key_file
}
```

### **Storage Module**
```hcl
module "storage" {
  source = "./modules/storage"
  
  project_id         = var.project_id
  region            = var.region
  environment       = var.environment
  lifecycle_age_days = var.storage_lifecycle_days
  storage_class     = var.storage_class
}
```

### **Tasks Module**
```hcl
module "tasks" {
  source = "./modules/tasks"
  
  project_id                = var.project_id
  region                   = var.region
  environment              = var.environment
  max_concurrent_dispatches = var.task_max_concurrent_dispatches
}
```

## 🎛️ Feature Flags

Control what gets deployed with simple boolean flags:

```hcl
# Feature flags
enable_database  = true   # Deploy Cloud SQL
enable_cloud_run = true   # Deploy Cloud Run
enable_secrets   = true   # Create Secret Manager secrets
create_local_key_file = false  # Don't create local key file
```

## 📊 Cost Optimization

### **Development**
```hcl
# Minimal resources for development
storage_lifecycle_days = 3
task_max_concurrent_dispatches = 2
cloud_run_min_instances = 0
```

### **Production**
```hcl
# Optimized for production
storage_lifecycle_days = 30
task_max_concurrent_dispatches = 10
cloud_run_min_instances = 1
cloud_run_cpu_limit = "2"
cloud_run_memory_limit = "2Gi"
```

## 🔄 Migration Path

### **From Monolithic to Modular**

1. **Backup current state:**
   ```bash
   cp main.tf main-backup.tf
   cp variables.tf variables-backup.tf
   ```

2. **Switch to modular:**
   ```bash
   ./deploy.sh dev-local plan
   ```

3. **Verify no changes:**
   The plan should show no changes if you're using the same configuration.

4. **Apply modular config:**
   ```bash
   ./deploy.sh dev-local apply
   ```

### **Upgrading Scenarios**

```bash
# From local to hybrid
./deploy.sh staging-hybrid plan
./deploy.sh staging-hybrid apply

# From hybrid to full cloud
./deploy.sh prod-cloud plan
./deploy.sh prod-cloud apply
```

## 🛠️ Advanced Usage

### **Custom Module Combinations**

```hcl
# Only storage and tasks (minimal cloud services)
module "core" { source = "./modules/core" }
module "storage" { source = "./modules/storage" }
module "tasks" { source = "./modules/tasks" }

# Database only (for testing)
module "core" { source = "./modules/core" }
module "database" { source = "./modules/database" }
```

### **Environment-Specific Configurations**

```hcl
# Development
storage_class = "STANDARD"
task_max_concurrent_dispatches = 2

# Staging  
storage_class = "STANDARD"
task_max_concurrent_dispatches = 5

# Production
storage_class = "STANDARD"
task_max_concurrent_dispatches = 10
```

## 🎯 Benefits

✅ **Modular**: Each component is independent
✅ **Reusable**: Modules can be used in different projects
✅ **Maintainable**: Easy to update individual components
✅ **Flexible**: Enable/disable features with flags
✅ **Cost-Optimized**: Pay only for what you use
✅ **Environment-Aware**: Different configs per environment
✅ **Easy Deployment**: One-command deployment scripts

## 📝 Next Steps

1. **Choose your scenario** and deploy with `./deploy.sh`
2. **Test the deployment** with your application
3. **Monitor costs** in Google Cloud Console
4. **Upgrade incrementally** as your needs grow
5. **Customize modules** for your specific requirements

The modular architecture gives you complete flexibility to deploy exactly what you need, when you need it, while keeping costs optimized and maintaining clean, organized infrastructure code.
