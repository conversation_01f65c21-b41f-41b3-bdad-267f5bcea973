using Microsoft.AspNetCore.Mvc;
using VidCompressor.Services;
using VidCompressor.WorkerService.Services;
using System.Text.Json;

namespace VidCompressor.WorkerService.Controllers;

[ApiController]
[Route("api/internal")]
public class InternalController : ControllerBase
{
    private readonly CompressionBackgroundService _compressionService;
    private readonly ILogger<InternalController> _logger;

    public InternalController(
        CompressionBackgroundService compressionService,
        ILogger<InternalController> logger)
    {
        _compressionService = compressionService;
        _logger = logger;
    }

    /// <summary>
    /// Internal endpoint for Cloud Tasks to trigger compression job processing
    /// </summary>
    [HttpPost("process-compression-job")]
    public async Task<IActionResult> ProcessCompressionJob([FromBody] ProcessJobRequest request)
    {
        try
        {
            _logger.LogInformation("Received compression job processing request for job: {JobId}", request.JobId);
            
            // Verify this is coming from Cloud Tasks (basic validation)
            var userAgent = Request.Headers.UserAgent.ToString();
            if (!userAgent.Contains("Google-Cloud-Tasks"))
            {
                _logger.LogWarning("Unauthorized request to internal endpoint from: {UserAgent}", userAgent);
                return Unauthorized("This endpoint is only accessible by Google Cloud Tasks");
            }

            await _compressionService.ProcessCompressionJobAsync(request.JobId);
            
            return Ok(new { message = "Job processing initiated", jobId = request.JobId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process compression job: {JobId}", request.JobId);
            return StatusCode(500, new { error = "Failed to process job", details = ex.Message });
        }
    }

    /// <summary>
    /// Health check endpoint for Cloud Tasks
    /// </summary>
    [HttpGet("health")]
    public IActionResult Health()
    {
        return Ok(new { status = "healthy", timestamp = DateTime.UtcNow });
    }
}

public class ProcessJobRequest
{
    public string JobId { get; set; } = string.Empty;
}
