using VidCompressor.Models;
using VidCompressor.Services;
using VidCompressor.Repositories;

namespace VidCompressor.WorkerService.Services;

/// <summary>
/// Background service that cleans up old compressed files and storage objects
/// </summary>
public class FileCleanupService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<FileCleanupService> _logger;
    private readonly TimeSpan _cleanupInterval = TimeSpan.FromHours(6); // Run every 6 hours
    private readonly TimeSpan _jobRetentionPeriod = TimeSpan.FromDays(1); // Keep jobs and previews for 24 hours

    public FileCleanupService(IServiceProvider serviceProvider, ILogger<FileCleanupService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("File cleanup service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CleanupOldFiles();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during file cleanup");
            }

            await Task.Delay(_cleanupInterval, stoppingToken);
        }
    }

    private async Task CleanupOldFiles()
    {
        using var scope = _serviceProvider.CreateScope();
        var compressionJobRepository = scope.ServiceProvider.GetRequiredService<ICompressionJobRepository>();
        var storageService = scope.ServiceProvider.GetRequiredService<GoogleCloudStorageService>();
        var creditsService = scope.ServiceProvider.GetRequiredService<CreditsService>();

        var cutoffDate = DateTime.UtcNow - _jobRetentionPeriod;

        _logger.LogInformation("Starting cleanup of jobs and files older than {CutoffDate}", cutoffDate);

        // Find all jobs older than retention period (24 hours from creation)
        var allJobs = await compressionJobRepository.GetAllAsync();
        var oldJobs = allJobs
            .Where(j => j.CreatedAt < cutoffDate)
            .ToList();

        // Separate completed and incomplete jobs
        var completedOldJobs = oldJobs.Where(j => j.CompletedAt.HasValue).ToList();
        var incompleteOldJobs = oldJobs.Where(j => !j.CompletedAt.HasValue).ToList();

        var cleanedLocalFiles = 0;
        var cleanedStorageFiles = 0;
        var refundedJobs = 0;

        // Process incomplete jobs first (refund credits and clean up)
        foreach (var job in incompleteOldJobs)
        {
            await ProcessIncompleteJobCleanup(job, compressionJobRepository, storageService, creditsService);
            refundedJobs++;
        }

        // Process completed jobs (just clean up files)
        foreach (var job in completedOldJobs)
        {
            bool jobUpdated = false;

            // Clean up local compressed files (temp files for batch upload)
            if (!string.IsNullOrEmpty(job.CompressedFilePath) && File.Exists(job.CompressedFilePath))
            {
                try
                {
                    File.Delete(job.CompressedFilePath);
                    job.CompressedFilePath = null;
                    jobUpdated = true;
                    cleanedLocalFiles++;
                    _logger.LogDebug("Deleted old local temp file for job {JobId}", job.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete old local temp file {FilePath} for job {JobId}",
                        job.CompressedFilePath, job.Id);
                }
            }

            // Clean up cloud storage files (compressed files for downloads)
            if (!string.IsNullOrEmpty(job.OutputStoragePath))
            {
                try
                {
                    await storageService.DeleteFileAsync(job.OutputStoragePath);
                    job.OutputStoragePath = null;
                    jobUpdated = true;
                    cleanedStorageFiles++;
                    _logger.LogDebug("Deleted old cloud storage file for job {JobId}", job.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete old cloud storage file {StoragePath} for job {JobId}",
                        job.OutputStoragePath, job.Id);
                }
            }

            // Clean up preview images
            if (!string.IsNullOrEmpty(job.PreviewImagePath))
            {
                try
                {
                    await storageService.DeleteFileAsync(job.PreviewImagePath);
                    job.PreviewImagePath = null;
                    jobUpdated = true;
                    cleanedStorageFiles++;
                    _logger.LogDebug("Deleted old preview image for job {JobId}", job.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete old preview image {PreviewPath} for job {JobId}",
                        job.PreviewImagePath, job.Id);
                }
            }

            // Update the job if any files were cleaned
            if (jobUpdated)
            {
                await compressionJobRepository.UpdateAsync(job);
            }
        }

        if (cleanedLocalFiles > 0 || cleanedStorageFiles > 0 || refundedJobs > 0)
        {
            _logger.LogInformation("Cleanup completed: {LocalFiles} local files, {StorageFiles} storage files deleted, {RefundedJobs} incomplete jobs refunded",
                cleanedLocalFiles, cleanedStorageFiles, refundedJobs);
        }
        else
        {
            _logger.LogDebug("No old files or incomplete jobs found for cleanup");
        }
    }

    private async Task ProcessIncompleteJobCleanup(
        CompressionJob job,
        ICompressionJobRepository compressionJobRepository,
        GoogleCloudStorageService storageService,
        CreditsService creditsService)
    {
        try
        {
            _logger.LogInformation("Processing cleanup for incomplete job {JobId} (Status: {Status}, Created: {CreatedAt})",
                job.Id, job.Status, job.CreatedAt);

            // Refund credits if they were deducted for this job
            if (job.CreditsUsed.HasValue && job.CreditsUsed.Value > 0)
            {
                var mediaTypeText = job.MediaType == MediaType.Photo ? "photo" : "video";
                var refundSuccess = await creditsService.RefundCreditsAsync(
                    job.UserId,
                    job.CreditsUsed.Value,
                    $"Refund for incomplete {mediaTypeText} compression (24-hour cleanup)",
                    job.Id);

                if (refundSuccess)
                {
                    _logger.LogInformation("Refunded {Credits} credits to user {UserId} for incomplete job {JobId}",
                        job.CreditsUsed.Value, job.UserId, job.Id);
                }
                else
                {
                    _logger.LogError("Failed to refund {Credits} credits to user {UserId} for incomplete job {JobId}",
                        job.CreditsUsed.Value, job.UserId, job.Id);
                }
            }

            // Clean up any associated files
            await CleanupJobFiles(job, storageService);

            // Delete the job from the database
            await compressionJobRepository.DeleteAsync(job.Id);

            _logger.LogInformation("Successfully cleaned up incomplete job {JobId}", job.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup incomplete job {JobId}", job.Id);
        }
    }

    private async Task CleanupJobFiles(CompressionJob job, GoogleCloudStorageService storageService)
    {
        // Clean up input storage files
        if (!string.IsNullOrEmpty(job.InputStoragePath))
        {
            try
            {
                await storageService.DeleteFileAsync(job.InputStoragePath);
                _logger.LogDebug("Deleted input storage file for job {JobId}: {InputPath}", job.Id, job.InputStoragePath);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to delete input storage file {InputPath} for job {JobId}", job.InputStoragePath, job.Id);
            }
        }

        // Clean up output storage files
        if (!string.IsNullOrEmpty(job.OutputStoragePath))
        {
            try
            {
                await storageService.DeleteFileAsync(job.OutputStoragePath);
                _logger.LogDebug("Deleted output storage file for job {JobId}: {OutputPath}", job.Id, job.OutputStoragePath);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to delete output storage file {OutputPath} for job {JobId}", job.OutputStoragePath, job.Id);
            }
        }

        // Clean up preview images
        if (!string.IsNullOrEmpty(job.PreviewImagePath))
        {
            try
            {
                await storageService.DeleteFileAsync(job.PreviewImagePath);
                _logger.LogDebug("Deleted preview image for job {JobId}: {PreviewPath}", job.Id, job.PreviewImagePath);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to delete preview image {PreviewPath} for job {JobId}", job.PreviewImagePath, job.Id);
            }
        }

        // Clean up local compressed files
        if (!string.IsNullOrEmpty(job.CompressedFilePath) && File.Exists(job.CompressedFilePath))
        {
            try
            {
                File.Delete(job.CompressedFilePath);
                _logger.LogDebug("Deleted local compressed file for job {JobId}: {CompressedPath}", job.Id, job.CompressedFilePath);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to delete local compressed file {CompressedPath} for job {JobId}", job.CompressedFilePath, job.Id);
            }
        }
    }
}
