{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Firestore": {"ProjectId": "demo-project", "ServiceAccountKeyPath": "", "DatabaseId": "(default)", "UseEmulator": true, "EmulatorHost": "localhost:8080"}, "GoogleCloud": {"ProjectId": "demo-project", "Region": "us-central1", "InputBucketName": "demo-project-vidcompressor-input", "OutputBucketName": "demo-project-vidcompressor-output", "TempBucketName": "demo-project-vidcompressor-temp", "ServiceAccountKeyPath": "/secrets/transcoder-service-account-key", "Transcoder": {"Location": "us-central1"}, "CloudTasks": {"ProjectId": "demo-project", "Location": "us-central1", "QueueName": "video-compression-jobs", "HandlerUrl": "https://your-worker-service-url"}}}