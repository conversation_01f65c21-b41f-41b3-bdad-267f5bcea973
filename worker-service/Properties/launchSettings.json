{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:30454", "sslPort": 44342}}, "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "http://localhost:5120", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "SignalR__ApiServerUrl": "http://localhost:5119"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "http://localhost:5120", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "SignalR__ApiServerUrl": "http://localhost:5119"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "SignalR__ApiServerUrl": "http://localhost:5119"}}}}