{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Firestore": {"ProjectId": "demo-project", "ServiceAccountKeyPath": "", "DatabaseId": "(default)", "UseEmulator": true, "EmulatorHost": "localhost:8080"}, "Google": {"ClientId": "************-oudag9d2btbee2n0m3ulh9c9pa5dr7fq.apps.googleusercontent.com", "ClientSecret": "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"}, "GoogleCloud": {"ProjectId": "demo-project", "Region": "us-central1", "CloudTasks": {"ProjectId": "demo-project", "Location": "us-central1", "QueueName": "video-compression-jobs", "HandlerUrl": "https://your-worker-service-url"}}, "Jwt": {"Secret": "a_super_secret_and_long_key_for_jwt_validation"}, "Stripe": {"PublishableKey": ""}}