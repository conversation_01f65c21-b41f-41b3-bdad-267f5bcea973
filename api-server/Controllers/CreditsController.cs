using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using VidCompressor.Models;
using VidCompressor.Data;
using VidCompressor.Services;

namespace VidCompressor.ApiServer.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CreditsController : ControllerBase
{
    private readonly CreditsService _creditsService;
    private readonly ILogger<CreditsController> _logger;

    public CreditsController(CreditsService creditsService, ILogger<CreditsController> logger)
    {
        _creditsService = creditsService;
        _logger = logger;
    }

    /// <summary>
    /// Get current user's credit balance
    /// </summary>
    [HttpGet("balance")]
    public async Task<IActionResult> GetBalance()
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new { message = "User not authenticated" });
            }

            var credits = await _creditsService.GetUserCreditsAsync(userId);
            
            return Ok(new { credits });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting credit balance");
            return StatusCode(500, new { message = "Error retrieving credit balance" });
        }
    }

    /// <summary>
    /// Get current user's credit transaction history
    /// </summary>
    [HttpGet("transactions")]
    public async Task<IActionResult> GetTransactions([FromQuery] int limit = 50)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new { message = "User not authenticated" });
            }

            var transactions = await _creditsService.GetUserTransactionHistoryAsync(userId, limit);
            
            var response = transactions.Select(t => new
            {
                id = t.Id,
                type = t.Type.ToString(),
                amount = t.Amount,
                balanceAfter = t.BalanceAfter,
                description = t.Description,
                compressionJobId = t.CompressionJobId,
                paymentId = t.PaymentId,
                createdAt = t.CreatedAt
            });

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting credit transactions");
            return StatusCode(500, new { message = "Error retrieving credit transactions" });
        }
    }

    /// <summary>
    /// Estimate credit cost for a compression operation
    /// </summary>
    [HttpPost("estimate")]
    public async Task<IActionResult> EstimateCost([FromBody] CreditCostEstimateRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var estimate = await _creditsService.EstimateCreditCostAsync(
                request.MediaType, 
                request.Quality, 
                request.DurationMinutes, 
                request.FileSizeBytes);

            return Ok(new
            {
                mediaType = estimate.MediaType.ToString(),
                quality = estimate.Quality,
                durationMinutes = estimate.DurationMinutes,
                fileSizeBytes = estimate.FileSizeBytes,
                estimatedCredits = estimate.EstimatedCredits,
                description = estimate.Description
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error estimating credit cost");
            return StatusCode(500, new { message = "Error estimating credit cost" });
        }
    }

    /// <summary>
    /// Get all active credit costs (for displaying pricing information)
    /// </summary>
    [HttpGet("costs")]
    public async Task<IActionResult> GetCreditCosts()
    {
        try
        {
            var costs = await _creditsService.GetActiveCreditCostsAsync();
            
            var response = costs.Select(c => new
            {
                id = c.Id,
                operationType = c.OperationType,
                cost = c.Cost,
                unit = c.Unit,
                description = c.Description
            });

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting credit costs");
            return StatusCode(500, new { message = "Error retrieving credit costs" });
        }
    }

    /// <summary>
    /// Check if user has sufficient credits for an operation
    /// </summary>
    [HttpPost("check")]
    public async Task<IActionResult> CheckSufficientCredits([FromBody] CheckCreditsRequest request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new { message = "User not authenticated" });
            }

            var hasSufficient = await _creditsService.HasSufficientCreditsAsync(userId, request.RequiredCredits);
            var currentBalance = await _creditsService.GetUserCreditsAsync(userId);

            return Ok(new
            {
                hasSufficientCredits = hasSufficient,
                currentBalance = currentBalance,
                requiredCredits = request.RequiredCredits,
                shortfall = hasSufficient ? 0 : request.RequiredCredits - currentBalance
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking credit sufficiency");
            return StatusCode(500, new { message = "Error checking credits" });
        }
    }
}

/// <summary>
/// Request model for credit cost estimation
/// </summary>
public class CreditCostEstimateRequest
{
    public MediaType MediaType { get; set; }
    public string Quality { get; set; } = string.Empty;
    public double? DurationMinutes { get; set; }
    public long? FileSizeBytes { get; set; }
}

/// <summary>
/// Request model for checking credit sufficiency
/// </summary>
public class CheckCreditsRequest
{
    public int RequiredCredits { get; set; }
}
