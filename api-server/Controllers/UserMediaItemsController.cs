using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using VidCompressor.Models;
using VidCompressor.Repositories;
using System.Text.Json;

namespace VidCompressor.ApiServer.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class UserMediaItemsController : ControllerBase
{
    private readonly IUserMediaItemRepository _userMediaItemRepository;
    private readonly ICompressionJobRepository _compressionJobRepository;
    private readonly ILogger<UserMediaItemsController> _logger;

    public UserMediaItemsController(
        IUserMediaItemRepository userMediaItemRepository,
        ICompressionJobRepository compressionJobRepository,
        ILogger<UserMediaItemsController> logger)
    {
        _userMediaItemRepository = userMediaItemRepository;
        _compressionJobRepository = compressionJobRepository;
        _logger = logger;
    }

    /// <summary>
    /// Get all media items for the current user
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<UserMediaItemResponse>>> GetUserMediaItems()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        try
        {
            var mediaItems = await _userMediaItemRepository.GetByUserIdAsync(userId);
            var mediaItemsList = mediaItems.ToList();

            // Get latest compression jobs for each media item
            var responses = new List<UserMediaItemResponse>();
            foreach (var item in mediaItemsList)
            {
                var latestJob = await _compressionJobRepository.GetLatestByUserAndMediaItemAsync(userId, item.GoogleMediaItemId);

                responses.Add(new UserMediaItemResponse
                {
                    Id = item.Id,
                    GoogleMediaItemId = item.GoogleMediaItemId,
                    Filename = item.Filename,
                    MimeType = item.MimeType,
                    BaseUrl = item.BaseUrl,
                    MediaType = item.MediaType,
                    Width = item.Width,
                    Height = item.Height,
                    FileSizeBytes = item.FileSizeBytes,
                    CreationTime = item.CreationTime,
                    Metadata = item.Metadata,
                    AddedAt = item.AddedAt,
                    LastAccessedAt = item.LastAccessedAt,
                    LatestCompressionJob = latestJob != null ? new CompressionJobSummary
                    {
                        Id = latestJob.Id,
                        Status = latestJob.Status,
                        Quality = latestJob.Quality,
                        UploadToGooglePhotos = latestJob.UploadToGooglePhotos,
                        CreatedAt = latestJob.CreatedAt,
                        CompletedAt = latestJob.CompletedAt,
                        ErrorMessage = latestJob.ErrorMessage,
                        OriginalSizeBytes = latestJob.OriginalSizeBytes,
                        CompressedSizeBytes = latestJob.CompressedSizeBytes,
                        CompressionRatio = latestJob.CompressionRatio
                    } : null
                });
            }

            // Update LastAccessedAt for all retrieved items
            var mediaItemIds = mediaItemsList.Select(mi => mi.Id);
            await _userMediaItemRepository.UpdateLastAccessedAsync(mediaItemIds);

            _logger.LogInformation("Retrieved {Count} media items for user {UserId}", responses.Count, userId);
            return Ok(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve media items for user {UserId}", userId);
            return StatusCode(500, new { error = "Failed to retrieve media items", details = ex.Message });
        }
    }

    /// <summary>
    /// Save multiple media items for the current user
    /// </summary>
    [HttpPost("batch")]
    public async Task<ActionResult<IEnumerable<UserMediaItemResponse>>> SaveMediaItems([FromBody] List<UserMediaItemRequest> requests)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        if (requests == null || !requests.Any())
        {
            return BadRequest("No media items provided");
        }

        try
        {
            var savedItems = new List<UserMediaItem>();
            var now = DateTime.UtcNow;

            foreach (var request in requests)
            {
                // Check if this media item already exists for this user
                var existingItem = await _userMediaItemRepository.GetByUserAndGoogleMediaItemIdAsync(userId, request.GoogleMediaItemId);

                if (existingItem != null)
                {
                    // Update existing item's LastAccessedAt and metadata
                    existingItem.LastAccessedAt = now;
                    existingItem.Filename = request.Filename;
                    existingItem.MimeType = request.MimeType;
                    existingItem.BaseUrl = request.BaseUrl;
                    existingItem.Width = request.Width;
                    existingItem.Height = request.Height;
                    existingItem.FileSizeBytes = request.FileSizeBytes;
                    existingItem.CreationTime = request.CreationTime;
                    existingItem.Metadata = request.Metadata;

                    await _userMediaItemRepository.UpdateAsync(existingItem);
                    savedItems.Add(existingItem);
                }
                else
                {
                    // Create new media item
                    var newItem = new UserMediaItem
                    {
                        Id = Guid.NewGuid().ToString(),
                        UserId = userId,
                        GoogleMediaItemId = request.GoogleMediaItemId,
                        Filename = request.Filename,
                        MimeType = request.MimeType,
                        BaseUrl = request.BaseUrl,
                        MediaType = request.MediaType,
                        Width = request.Width,
                        Height = request.Height,
                        FileSizeBytes = request.FileSizeBytes,
                        CreationTime = request.CreationTime,
                        Metadata = request.Metadata,
                        AddedAt = now,
                        LastAccessedAt = now
                    };

                    await _userMediaItemRepository.AddAsync(newItem);
                    savedItems.Add(newItem);
                }
            }

            // No need for SaveChangesAsync with repositories - each operation is committed individually

            // Convert to response DTOs
            var responses = savedItems.Select(item => new UserMediaItemResponse
            {
                Id = item.Id,
                GoogleMediaItemId = item.GoogleMediaItemId,
                Filename = item.Filename,
                MimeType = item.MimeType,
                BaseUrl = item.BaseUrl,
                MediaType = item.MediaType,
                Width = item.Width,
                Height = item.Height,
                FileSizeBytes = item.FileSizeBytes,
                CreationTime = item.CreationTime,
                Metadata = item.Metadata,
                AddedAt = item.AddedAt,
                LastAccessedAt = item.LastAccessedAt
            }).ToList();

            _logger.LogInformation("Saved {Count} media items for user {UserId}", savedItems.Count, userId);
            return Ok(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save media items for user {UserId}", userId);
            return StatusCode(500, new { error = "Failed to save media items", details = ex.Message });
        }
    }

    /// <summary>
    /// Delete a specific media item for the current user
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteMediaItem(string id)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        try
        {
            var mediaItem = await _userMediaItemRepository.GetByIdAsync(id);

            if (mediaItem == null || mediaItem.UserId != userId)
            {
                return NotFound();
            }

            await _userMediaItemRepository.DeleteAsync(id);

            _logger.LogInformation("Deleted media item {MediaItemId} for user {UserId}", id, userId);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete media item {MediaItemId} for user {UserId}", id, userId);
            return StatusCode(500, new { error = "Failed to delete media item", details = ex.Message });
        }
    }

    /// <summary>
    /// Delete multiple media items for the current user
    /// </summary>
    [HttpDelete("batch")]
    public async Task<IActionResult> DeleteMediaItems([FromBody] List<string> ids)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        if (ids == null || !ids.Any())
        {
            return BadRequest("No media item IDs provided");
        }

        try
        {
            var deletedCount = 0;
            foreach (var id in ids)
            {
                // Verify the item belongs to the user before deleting
                var item = await _userMediaItemRepository.GetByIdAsync(id);
                if (item != null && item.UserId == userId)
                {
                    var deleted = await _userMediaItemRepository.DeleteAsync(id);
                    if (deleted) deletedCount++;
                }
            }

            _logger.LogInformation("Deleted {Count} media items for user {UserId}", deletedCount, userId);
            return Ok(new { deletedCount });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete media items for user {UserId}", userId);
            return StatusCode(500, new { error = "Failed to delete media items", details = ex.Message });
        }
    }

    /// <summary>
    /// Get a specific media item by Google Media Item ID
    /// </summary>
    [HttpGet("by-google-id/{googleMediaItemId}")]
    public async Task<ActionResult<UserMediaItemResponse>> GetMediaItemByGoogleId(string googleMediaItemId)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        try
        {
            var mediaItem = await _userMediaItemRepository.GetByUserAndGoogleMediaItemIdAsync(userId, googleMediaItemId);

            if (mediaItem == null)
            {
                return NotFound();
            }

            // Get latest compression job
            var latestJob = await _compressionJobRepository.GetLatestByUserAndMediaItemAsync(userId, mediaItem.GoogleMediaItemId);

            var response = new UserMediaItemResponse
            {
                Id = mediaItem.Id,
                GoogleMediaItemId = mediaItem.GoogleMediaItemId,
                Filename = mediaItem.Filename,
                MimeType = mediaItem.MimeType,
                BaseUrl = mediaItem.BaseUrl,
                MediaType = mediaItem.MediaType,
                Width = mediaItem.Width,
                Height = mediaItem.Height,
                FileSizeBytes = mediaItem.FileSizeBytes,
                CreationTime = mediaItem.CreationTime,
                Metadata = mediaItem.Metadata,
                AddedAt = mediaItem.AddedAt,
                LastAccessedAt = mediaItem.LastAccessedAt,
                LatestCompressionJob = latestJob != null ? new CompressionJobSummary
                {
                    Id = latestJob.Id,
                    Status = latestJob.Status,
                    Quality = latestJob.Quality,
                    UploadToGooglePhotos = latestJob.UploadToGooglePhotos,
                    CreatedAt = latestJob.CreatedAt,
                    CompletedAt = latestJob.CompletedAt,
                    ErrorMessage = latestJob.ErrorMessage,
                    OriginalSizeBytes = latestJob.OriginalSizeBytes,
                    CompressedSizeBytes = latestJob.CompressedSizeBytes,
                    CompressionRatio = latestJob.CompressionRatio
                } : null
            };

            // Update LastAccessedAt
            await _userMediaItemRepository.UpdateLastAccessedAsync(new[] { mediaItem.Id });

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve media item {GoogleMediaItemId} for user {UserId}", googleMediaItemId, userId);
            return StatusCode(500, new { error = "Failed to retrieve media item", details = ex.Message });
        }
    }
}
