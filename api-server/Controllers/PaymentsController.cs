using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using Stripe.Checkout;
using System.Collections.Generic;
using System.Security.Claims;
using VidCompressor.Services;

[Route("api/[controller]")]
[Authorize]
public class PaymentsController : Controller
{
    private readonly CreditsService _creditsService;
    private readonly ILogger<PaymentsController> _logger;
    private readonly IConfiguration _configuration;

    public PaymentsController(CreditsService creditsService, ILogger<PaymentsController> logger, IConfiguration configuration)
    {
        _creditsService = creditsService;
        _logger = logger;
        _configuration = configuration;
    }
    [HttpPost("create-checkout-session")]
    public ActionResult CreateCheckoutSession()
    {
        var options = new SessionCreateOptions
        {
            LineItems = new List<SessionLineItemOptions>
            {
                new SessionLineItemOptions
                {
                    Price = "{{PRICE_ID}}", // Replace with your price ID
                    Quantity = 1,
                },
            },
            Mode = "subscription",
            SuccessUrl = "http://localhost:3000/success",
            CancelUrl = "http://localhost:3000/cancel",
        };

        var service = new SessionService();
        Session session = service.Create(options);

        return Json(new { id = session.Id });
    }

    [HttpPost("create-credit-checkout-session")]
    public ActionResult CreateCreditCheckoutSession([FromBody] CreateCreditCheckoutRequest request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new { message = "User not authenticated" });
            }

            // Define credit pack configurations
            var creditPacks = new Dictionary<string, (int credits, decimal price, string name)>
            {
                ["starter"] = (500, 4.99m, "Starter Pack"),
                ["best-value"] = (1200, 9.99m, "Best Value Pack"),
                ["premium"] = (2500, 19.99m, "Premium Pack"),
                ["enterprise"] = (5000, 34.99m, "Enterprise Pack")
            };

            if (!creditPacks.ContainsKey(request.CreditPackId))
            {
                return BadRequest(new { message = "Invalid credit pack ID" });
            }

            var (credits, price, name) = creditPacks[request.CreditPackId];

            var frontEndBase = _configuration["Frontend:BaseUrl"] ?? $"{Request.Scheme}://{Request.Host}";
            var options = new SessionCreateOptions
            {
                LineItems = new List<SessionLineItemOptions>
                {
                    new SessionLineItemOptions
                    {
                        PriceData = new SessionLineItemPriceDataOptions
                        {
                            UnitAmount = (long)(price * 100), // Convert to cents
                            Currency = "usd",
                            ProductData = new SessionLineItemPriceDataProductDataOptions
                            {
                                Name = $"{name} - {credits:N0} Credits",
                                Description = $"Add {credits:N0} credits to your Gallery Tuner account"
                            }
                        },
                        Quantity = 1,
                    },
                },
                Mode = "payment",
                SuccessUrl = $"{frontEndBase}/success?session_id={{CHECKOUT_SESSION_ID}}",
                CancelUrl = $"{frontEndBase}/add-credits",
                ClientReferenceId = userId,
                Metadata = new Dictionary<string, string>
                {
                    ["user_id"] = userId,
                    ["credit_pack_id"] = request.CreditPackId,
                    ["credits"] = credits.ToString()
                }
            };

            var service = new SessionService();
            Session session = service.Create(options);

            _logger.LogInformation("Created credit checkout session {SessionId} for user {UserId}, pack {PackId}",
                session.Id, userId, request.CreditPackId);

            return Json(new { sessionId = session.Id });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating credit checkout session for user {UserId}",
                User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
            return StatusCode(500, new { message = "Error creating checkout session" });
        }
    }
}

public class CreateCreditCheckoutRequest
{
    public string CreditPackId { get; set; } = string.Empty;
    public string PriceId { get; set; } = string.Empty;
}
