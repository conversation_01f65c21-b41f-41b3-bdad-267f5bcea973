# Environment variables for API Server Cloud Run deployment
# This file is used with --env-vars-file flag in gcloud run deploy

ASPNETCORE_ENVIRONMENT: Production

# Firestore Configuration
Firestore__ProjectId: tranquil-bison-465923-v9
Firestore__DatabaseId: gallerytuner
Firestore__UseEmulator: "false"
Firestore__EmulatorHost: ""

# Google Cloud Configuration
GoogleCloud__ProjectId: tranquil-bison-465923-v9
GoogleCloud__Region: us-central1
GoogleCloud__InputBucketName: tranquil-bison-465923-v9-vidcompressor-input
GoogleCloud__OutputBucketName: tranquil-bison-465923-v9-vidcompressor-output
GoogleCloud__TempBucketName: tranquil-bison-465923-v9-vidcompressor-temp

# Cloud Tasks Configuration
GoogleCloud__CloudTasks__ProjectId: tranquil-bison-465923-v9
GoogleCloud__CloudTasks__Location: us-central1
GoogleCloud__CloudTasks__QueueName: video-compression-jobs
GoogleCloud__CloudTasks__HandlerUrl: https://vidcompressor-worker-service-546390650743.us-central1.run.app

# Transcoder Configuration
GoogleCloud__Transcoder__Location: us-central1

# Google OAuth Configuration (non-sensitive)
Google__ClientId: "546390650743-oudag9d2btbee2n0m3ulh9c9pa5dr7fq.apps.googleusercontent.com"
Google__RedirectUri: "https://gallerytuner.com/auth/callback"

# Stripe Configuration (served via Secret Manager)
# Stripe__PublishableKey is loaded from Secret Manager as stripe-publishable-key-prod

# Note: Sensitive values are injected via Secret Manager:
# - Google__ClientSecret (from google-client-secret-prod)
# - Jwt__Secret (from jwt-secret-prod)
# - Stripe__SecretKey (from stripe-secret-key-prod)
# - Stripe__WebhookSecret (from stripe-webhook-secret-prod)
