{"version": "0.2.0", "configurations": [{"name": "API Server", "type": "coreclr", "request": "launch", "preLaunchTask": "build-api-server", "program": "${workspaceFolder}/api-server/bin/Debug/net8.0/api-server.dll", "args": [], "cwd": "${workspaceFolder}/api-server", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5119"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Worker Service", "type": "coreclr", "request": "launch", "preLaunchTask": "build-worker-service", "program": "${workspaceFolder}/worker-service/bin/Debug/net8.0/worker-service.dll", "args": [], "cwd": "${workspaceFolder}/worker-service", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5120", "SignalR__ApiServerUrl": "http://localhost:5119"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Both Services", "type": "coreclr", "request": "launch", "preLaunchTask": "build-all", "program": "${workspaceFolder}/api-server/bin/Debug/net8.0/api-server.dll", "args": [], "cwd": "${workspaceFolder}/api-server", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5119"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}, "postDebugTask": "launch-worker-service"}, {"name": ".NET Core Launch (legacy backend)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/backend/bin/Debug/net8.0/backend.dll", "args": [], "cwd": "${workspaceFolder}/backend", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}], "compounds": [{"name": "Launch Both Services", "configurations": ["API Server", "Worker Service"], "stopAll": true}]}