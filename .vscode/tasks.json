{"version": "2.0.0", "tasks": [{"label": "build-api-server", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/api-server/api-server.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "build-worker-service", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/worker-service/worker-service.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "build-shared", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/shared/shared.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "build-all", "dependsOrder": "sequence", "dependsOn": ["build-shared", "build-api-server", "build-worker-service"], "group": {"kind": "build", "isDefault": true}}, {"label": "launch-worker-service", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/worker-service/worker-service.csproj"], "options": {"env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://localhost:5120", "SignalR__ApiServerUrl": "http://localhost:5119"}}, "isBackground": true, "problemMatcher": "$msCompile"}, {"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/backend/backend.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}]}