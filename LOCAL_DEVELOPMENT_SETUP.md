# Local Development Setup with Google Secret Manager

## 🎯 **Direct Secret Manager Integration**

Your application now connects **directly to Google Secret Manager** at startup - no local secret files needed!

## 🚀 **Quick Setup**

### 1. **Authenticate with Google Cloud**
```bash
# Authenticate your user account
gcloud auth login

# Set up application default credentials
gcloud auth application-default login

# Set your project
gcloud config set project tranquil-bison-465923-v9
```

### 2. **Ensure Secrets Exist**
```bash
# Create development secrets (one-time setup)
./deployment/setup-secrets.sh dev
```

### 3. **Run Your Application**
```bash
# API Server
cd api-server
dotnet run

# Worker Service  
cd worker-service
dotnet run
```

**That's it!** Your app will automatically:
- ✅ Connect to Google Secret Manager at startup
- ✅ Load all required secrets (Google OAuth, Stripe, etc.)
- ✅ Fall back gracefully if secrets are missing in development
- ✅ Use the same configuration system as production

## 🔧 **How It Works**

### **Automatic Secret Loading**
When your app starts, it:

1. **Detects environment** (Development/Production)
2. **Connects to Secret Manager** using your gcloud credentials
3. **Loads secrets** directly into configuration
4. **Maps secrets** to the right configuration keys:
   - `google-client-secret-dev` → `Google:ClientSecret`
   - `stripe-secret-key-dev` → `Stripe:SecretKey`
   - `stripe-webhook-secret-dev` → `Stripe:WebhookSecret`

### **Configuration Priority**
Your app loads configuration in this order:
1. **appsettings.json** (base configuration)
2. **appsettings.Development.json** (environment-specific)
3. **Environment variables** (if any)
4. **Google Secret Manager** (secrets override everything)

### **Development vs Production**
- **Development**: Missing secrets won't crash the app (graceful fallback)
- **Production**: Missing secrets will fail startup (fail-fast for security)

## 🛠️ **Benefits of This Approach**

### ✅ **Security**
- **No secrets in files** - never accidentally commit them
- **No local storage** - secrets stay in Google's secure infrastructure
- **Audit trail** - Google logs all secret access
- **Automatic rotation** - update secrets in one place

### ✅ **Developer Experience**
- **Zero configuration** - works out of the box after gcloud auth
- **Consistent with production** - same secret loading mechanism
- **No manual sync** - always get latest secret values
- **IDE friendly** - IntelliSense works with configuration keys

### ✅ **Operations**
- **Environment isolation** - dev/staging/prod secrets are separate
- **Easy secret management** - use Google Cloud Console or gcloud CLI
- **No deployment complexity** - secrets are loaded at runtime
- **Monitoring** - track secret access in Google Cloud Logging

## 🔍 **Troubleshooting**

### **"Failed to load secrets" Error**
```bash
# Check authentication
gcloud auth list

# Re-authenticate if needed
gcloud auth application-default login

# Verify project access
gcloud projects describe tranquil-bison-465923-v9
```

### **"Secret not found" Warning**
```bash
# List available secrets
gcloud secrets list --project=tranquil-bison-465923-v9

# Create missing secrets
./deployment/setup-secrets.sh dev
```

### **Permission Denied**
```bash
# Check IAM permissions
gcloud projects get-iam-policy tranquil-bison-465923-v9 \
  --flatten="bindings[].members" \
  --filter="bindings.members:user:$(gcloud config get-value account)"

# You need: roles/secretmanager.secretAccessor
```

## 🎯 **Local Development Workflow**

### **Daily Development**
```bash
# Just run your app - secrets load automatically
dotnet run --project api-server
```

### **Adding New Secrets**
```bash
# Add to Secret Manager
echo -n "new-secret-value" | gcloud secrets versions add my-new-secret-dev --data-file=-

# Update SecretManagerConfigurationProvider.cs to map the new secret
# No other changes needed!
```

### **Testing Different Environments**
```bash
# Test with staging secrets
export ASPNETCORE_ENVIRONMENT=Staging
dotnet run

# Test with production secrets (be careful!)
export ASPNETCORE_ENVIRONMENT=Production
dotnet run
```

## 🚨 **Security Best Practices**

1. **Never commit secrets** - they're loaded at runtime now
2. **Use separate environments** - dev/staging/prod secrets are isolated
3. **Rotate secrets regularly** - easy to do in Google Cloud Console
4. **Monitor access** - check Cloud Logging for secret access patterns
5. **Principle of least privilege** - only grant necessary Secret Manager permissions

## 🎉 **Next Steps**

1. **Remove old secret files** - `.env.local`, hardcoded values in appsettings
2. **Update your team** - share this setup guide
3. **Set up CI/CD** - same approach works in build pipelines
4. **Monitor secret usage** - set up alerts for failed secret access

Your local development now uses the **same secure secret management** as production! 🔒
