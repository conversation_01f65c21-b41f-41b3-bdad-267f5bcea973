- photos
    - input
        - saved to local storage, cleared from internal storage after processing (?)
    - output
        - saved to output bucket, deleted after 24 hours with bucket policy
    - preview
        - right now: not saved anywhere, expires after 1hr
- videos
    - input
        - saved to input bucket, deleted after transcoding finishes, backup 7 day deletion policy on input bucket
    - output
        - saved to output bucket, deleted after 24 hours with bucket policy
    - preview
        - right now: not saved anywhere, expires after 1hr 
- overall TODO:
    - save preview image along with job so it doesn't expire in 1 hr but 24 hrs instead
    - show that preview in the job history instead of the preview direct from google photos
    - delete job AND preview 24 hours after creation
        - if the job hasn't completed at this point the credits should be refunded 
    - delete job AND preview when user manually clears job
    - the jobs panel should have small text to let the user know that jobs and associated data are deleted after 24 hours