# This file specifies files that are *not* uploaded to Google Cloud
# when using gcloud. It follows the same syntax as .gitignore, with the addition of
# "#!include" directives (which insert the entries of the given .gitignore-style
# file at that point).
#
# For more information, run:
#   $ gcloud topic gcloudignore
#
.gcloudignore
# If you would like to upload your .git directory, .gitignore file or files
# from your .gitignore file, remove the corresponding line below:
.git
.gitignore

# Node.js dependencies:
frontend/node_modules/

# Build outputs that aren't needed for Docker builds
**/bin/
**/obj/
**/out/

# IDE files
.vs/
.vscode/
*.user
*.suo
*.userosscache
*.sln.docstates

# OS files
.DS_Store
Thumbs.db

# Documentation (not needed for builds)
*.md
CONFIGURATION.md
MIGRATION_STRATEGY.md
README.md
TESTING_CHECKLIST.md

# Terraform (not needed for Docker builds)
terraform/

# Test files
test-compression-api.http

# Logs
*.log
firestore-debug.log

# Temporary files
*.tmp
*.temp

# Package files
*.nupkg
*.snupkg

# Other files not needed for builds
issues.txt
todo.md
gallery_tuner.ico
firebase.json
run-local-*.sh
test-docker-builds.sh

# Frontend build output (not needed for backend Docker builds)
frontend/build/

# But make sure we include essential files for .NET builds:
!*.sln
!**/*.csproj
!**/Program.cs
!**/appsettings*.json
!shared/**/*.cs
!api-server/**/*.cs
!worker-service/**/*.cs
