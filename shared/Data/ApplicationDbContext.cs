using Google.Cloud.Firestore;

namespace VidCompressor.Data;

/// <summary>
/// Firestore database context for managing collections and documents
/// </summary>
public class FirestoreDbContext
{
    private readonly FirestoreDb _firestoreDb;

    public FirestoreDbContext(FirestoreDb firestoreDb)
    {
        _firestoreDb = firestoreDb;
    }

    public FirestoreDb Database => _firestoreDb;

    // Collection references
    public CollectionReference Users => _firestoreDb.Collection("users");
    public CollectionReference CompressionJobs => _firestoreDb.Collection("compressionJobs");
    public CollectionReference UserMediaItems => _firestoreDb.Collection("userMediaItems");
    public CollectionReference EmailSignups => _firestoreDb.Collection("emailSignups");
    public CollectionReference CreditTransactions => _firestoreDb.Collection("creditTransactions");
    public CollectionReference CreditCosts => _firestoreDb.Collection("creditCosts");
}
