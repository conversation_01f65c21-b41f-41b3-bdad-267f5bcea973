using Google.Cloud.Firestore;
using Microsoft.Extensions.Logging;
using VidCompressor.Data;
using VidCompressor.Models;
using VidCompressor.Models.Documents;

namespace VidCompressor.Repositories;

/// <summary>
/// Firestore implementation of EmailSignup repository
/// </summary>
public class EmailSignupRepository : IEmailSignupRepository
{
    private readonly FirestoreDbContext _context;
    private readonly ILogger<EmailSignupRepository> _logger;

    public EmailSignupRepository(FirestoreDbContext context, ILogger<EmailSignupRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<EmailSignup?> GetByIdAsync(string id)
    {
        try
        {
            var docRef = _context.EmailSignups.Document(id);
            var snapshot = await docRef.GetSnapshotAsync();
            
            if (!snapshot.Exists)
            {
                return null;
            }

            var signupDoc = snapshot.ConvertTo<EmailSignupDocument>();
            return signupDoc.ToEmailSignup();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get email signup by ID: {SignupId}", id);
            throw;
        }
    }

    public async Task<EmailSignup?> GetByEmailAsync(string email)
    {
        try
        {
            var query = _context.EmailSignups.WhereEqualTo("email", email).Limit(1);
            var snapshot = await query.GetSnapshotAsync();
            
            if (snapshot.Count == 0)
            {
                return null;
            }

            var signupDoc = snapshot.Documents.First().ConvertTo<EmailSignupDocument>();
            return signupDoc.ToEmailSignup();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get email signup by email: {Email}", email);
            throw;
        }
    }

    public async Task<IEnumerable<EmailSignup>> GetAllAsync()
    {
        try
        {
            var snapshot = await _context.EmailSignups.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<EmailSignupDocument>().ToEmailSignup());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all email signups");
            throw;
        }
    }

    public async Task<(IEnumerable<EmailSignup> signups, int totalCount)> GetPaginatedAsync(int page, int pageSize)
    {
        try
        {
            // Get total count first
            var totalSnapshot = await _context.EmailSignups.GetSnapshotAsync();
            var totalCount = totalSnapshot.Count;

            // Get paginated results
            var skip = (page - 1) * pageSize;
            var query = _context.EmailSignups
                .OrderByDescending("createdAt")
                .Offset(skip)
                .Limit(pageSize);
            
            var snapshot = await query.GetSnapshotAsync();
            var signups = snapshot.Documents.Select(doc => doc.ConvertTo<EmailSignupDocument>().ToEmailSignup());

            return (signups, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get paginated email signups");
            throw;
        }
    }

    public async Task<int> GetCountAsync()
    {
        try
        {
            var snapshot = await _context.EmailSignups.GetSnapshotAsync();
            return snapshot.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get email signup count");
            throw;
        }
    }

    public async Task<EmailSignup> AddAsync(EmailSignup entity)
    {
        try
        {
            // Generate a new ID if not set
            if (entity.Id == 0)
            {
                entity.Id = await GetNextIdAsync();
            }

            var signupDoc = EmailSignupDocument.FromEmailSignup(entity);
            var docRef = _context.EmailSignups.Document(entity.Id.ToString());
            await docRef.SetAsync(signupDoc);
            
            _logger.LogInformation("Added email signup: {SignupId}", entity.Id);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add email signup: {Email}", entity.Email);
            throw;
        }
    }

    public async Task<EmailSignup> UpdateAsync(EmailSignup entity)
    {
        try
        {
            var signupDoc = EmailSignupDocument.FromEmailSignup(entity);
            var docRef = _context.EmailSignups.Document(entity.Id.ToString());
            await docRef.SetAsync(signupDoc, SetOptions.MergeAll);
            
            _logger.LogInformation("Updated email signup: {SignupId}", entity.Id);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update email signup: {SignupId}", entity.Id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(string id)
    {
        try
        {
            var docRef = _context.EmailSignups.Document(id);
            await docRef.DeleteAsync();
            
            _logger.LogInformation("Deleted email signup: {SignupId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete email signup: {SignupId}", id);
            return false;
        }
    }

    public async Task<bool> ExistsAsync(string id)
    {
        try
        {
            var docRef = _context.EmailSignups.Document(id);
            var snapshot = await docRef.GetSnapshotAsync();
            return snapshot.Exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if email signup exists: {SignupId}", id);
            throw;
        }
    }

    public async Task<bool> MarkAsNotifiedAsync(string id)
    {
        try
        {
            var docRef = _context.EmailSignups.Document(id);
            var updates = new Dictionary<string, object>
            {
                { "isNotified", true },
                { "notifiedAt", Timestamp.GetCurrentTimestamp() }
            };
            
            await docRef.UpdateAsync(updates);
            
            _logger.LogInformation("Marked email signup as notified: {SignupId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark email signup as notified: {SignupId}", id);
            return false;
        }
    }

    /// <summary>
    /// Generate next sequential ID for email signup
    /// </summary>
    private async Task<int> GetNextIdAsync()
    {
        try
        {
            var snapshot = await _context.EmailSignups.GetSnapshotAsync();
            if (snapshot.Count == 0)
            {
                return 1;
            }

            var maxId = 0;
            foreach (var doc in snapshot.Documents)
            {
                if (int.TryParse(doc.Id, out var id) && id > maxId)
                {
                    maxId = id;
                }
            }

            return maxId + 1;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate next ID for email signup");
            throw;
        }
    }
}
