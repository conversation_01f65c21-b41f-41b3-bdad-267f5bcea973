using VidCompressor.Models;

namespace VidCompressor.Repositories;

/// <summary>
/// Repository interface for User entity operations
/// </summary>
public interface IUserRepository : IRepository<User>
{
    /// <summary>
    /// Get user by email address
    /// </summary>
    Task<User?> GetByEmailAsync(string email);

    /// <summary>
    /// Update user's credit balance
    /// </summary>
    Task<bool> UpdateCreditsAsync(string userId, int newBalance);

    /// <summary>
    /// Update user's Google tokens
    /// </summary>
    Task<bool> UpdateGoogleTokensAsync(string userId, string? accessToken, DateTime? expiry, string? refreshToken);
}
