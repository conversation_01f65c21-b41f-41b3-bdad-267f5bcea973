using Google.Cloud.Firestore;
using Microsoft.Extensions.Logging;
using VidCompressor.Data;
using VidCompressor.Models;
using VidCompressor.Models.Documents;

namespace VidCompressor.Repositories;

/// <summary>
/// Firestore implementation of User repository
/// </summary>
public class UserRepository : IUserRepository
{
    private readonly FirestoreDbContext _context;
    private readonly ILogger<UserRepository> _logger;

    public UserRepository(FirestoreDbContext context, ILogger<UserRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<User?> GetByIdAsync(string id)
    {
        try
        {
            var docRef = _context.Users.Document(id);
            var snapshot = await docRef.GetSnapshotAsync();
            
            if (!snapshot.Exists)
            {
                return null;
            }

            var userDoc = snapshot.ConvertTo<UserDocument>();
            return userDoc.ToUser();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user by ID: {UserId}", id);
            throw;
        }
    }

    public async Task<User?> GetByEmailAsync(string email)
    {
        try
        {
            var query = _context.Users.WhereEqualTo("email", email).Limit(1);
            var snapshot = await query.GetSnapshotAsync();
            
            if (snapshot.Count == 0)
            {
                return null;
            }

            var userDoc = snapshot.Documents.First().ConvertTo<UserDocument>();
            return userDoc.ToUser();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user by email: {Email}", email);
            throw;
        }
    }

    public async Task<IEnumerable<User>> GetAllAsync()
    {
        try
        {
            var snapshot = await _context.Users.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<UserDocument>().ToUser());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all users");
            throw;
        }
    }

    public async Task<User> AddAsync(User entity)
    {
        try
        {
            var userDoc = UserDocument.FromUser(entity);
            var docRef = _context.Users.Document(entity.Id);
            await docRef.SetAsync(userDoc);
            
            _logger.LogInformation("Added user: {UserId}", entity.Id);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add user: {UserId}", entity.Id);
            throw;
        }
    }

    public async Task<User> UpdateAsync(User entity)
    {
        try
        {
            var userDoc = UserDocument.FromUser(entity);
            var docRef = _context.Users.Document(entity.Id);
            await docRef.SetAsync(userDoc, SetOptions.MergeAll);
            
            _logger.LogInformation("Updated user: {UserId}", entity.Id);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update user: {UserId}", entity.Id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(string id)
    {
        try
        {
            var docRef = _context.Users.Document(id);
            await docRef.DeleteAsync();
            
            _logger.LogInformation("Deleted user: {UserId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete user: {UserId}", id);
            return false;
        }
    }

    public async Task<bool> ExistsAsync(string id)
    {
        try
        {
            var docRef = _context.Users.Document(id);
            var snapshot = await docRef.GetSnapshotAsync();
            return snapshot.Exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if user exists: {UserId}", id);
            throw;
        }
    }

    public async Task<bool> UpdateCreditsAsync(string userId, int newBalance)
    {
        try
        {
            var docRef = _context.Users.Document(userId);
            await docRef.UpdateAsync(new Dictionary<string, object>
            {
                { "credits", newBalance },
                { "updatedAt", Timestamp.GetCurrentTimestamp() }
            });
            
            _logger.LogInformation("Updated credits for user {UserId} to {Balance}", userId, newBalance);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update credits for user: {UserId}", userId);
            return false;
        }
    }

    public async Task<bool> UpdateGoogleTokensAsync(string userId, string? accessToken, DateTime? expiry, string? refreshToken)
    {
        try
        {
            var updates = new Dictionary<string, object>
            {
                { "googleAccessToken", accessToken ?? "" },
                { "googleRefreshToken", refreshToken ?? "" },
                { "updatedAt", Timestamp.GetCurrentTimestamp() }
            };

            if (expiry.HasValue)
            {
                updates["googleTokenExpiry"] = expiry.Value.ToTimestamp();
            }

            var docRef = _context.Users.Document(userId);
            await docRef.UpdateAsync(updates);
            
            _logger.LogInformation("Updated Google tokens for user: {UserId}", userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update Google tokens for user: {UserId}", userId);
            return false;
        }
    }
}
