using VidCompressor.Models;

namespace VidCompressor.Repositories;

/// <summary>
/// Repository interface for CompressionJob entity operations
/// </summary>
public interface ICompressionJobRepository : IRepository<CompressionJob>
{
    /// <summary>
    /// Get compression jobs for a specific user
    /// </summary>
    Task<IEnumerable<CompressionJob>> GetByUserIdAsync(string userId);

    /// <summary>
    /// Get compression jobs for a specific user with optional limit
    /// </summary>
    Task<IEnumerable<CompressionJob>> GetByUserIdAsync(string userId, int limit);

    /// <summary>
    /// Get compression jobs by status
    /// </summary>
    Task<IEnumerable<CompressionJob>> GetByStatusAsync(CompressionJobStatus status);

    /// <summary>
    /// Get compression jobs for a specific user and media item
    /// </summary>
    Task<IEnumerable<CompressionJob>> GetByUserAndMediaItemAsync(string userId, string mediaItemId);

    /// <summary>
    /// Get the latest compression job for a specific user and media item
    /// </summary>
    Task<CompressionJob?> GetLatestByUserAndMediaItemAsync(string userId, string mediaItemId);

    /// <summary>
    /// Update job status
    /// </summary>
    Task<bool> UpdateStatusAsync(string jobId, CompressionJobStatus status, string? errorMessage = null);

    /// <summary>
    /// Update job with completion details
    /// </summary>
    Task<bool> UpdateCompletionAsync(string jobId, long? compressedSizeBytes, double? compressionRatio, string? outputPath);

    /// <summary>
    /// Get deletable compression jobs for a user (completed, failed, or cancelled)
    /// </summary>
    Task<IEnumerable<CompressionJob>> GetDeletableJobsByUserIdAsync(string userId);

    /// <summary>
    /// Delete multiple compression jobs by their IDs
    /// </summary>
    Task<bool> DeleteMultipleAsync(IEnumerable<string> jobIds);

    /// <summary>
    /// Get old compression jobs for cleanup (older than specified date)
    /// </summary>
    Task<IEnumerable<CompressionJob>> GetOldJobsByUserIdAsync(string userId, DateTime cutoffDate);
}
