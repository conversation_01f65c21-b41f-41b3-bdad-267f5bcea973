using Google.Cloud.Firestore;
using Microsoft.Extensions.Logging;
using VidCompressor.Data;
using VidCompressor.Models;
using VidCompressor.Models.Documents;

namespace VidCompressor.Repositories;

/// <summary>
/// Firestore implementation of CreditCost repository
/// </summary>
public class CreditCostRepository : ICreditCostRepository
{
    private readonly FirestoreDbContext _context;
    private readonly ILogger<CreditCostRepository> _logger;

    public CreditCostRepository(FirestoreDbContext context, ILogger<CreditCostRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<CreditCost?> GetByIdAsync(string id)
    {
        try
        {
            var docRef = _context.CreditCosts.Document(id);
            var snapshot = await docRef.GetSnapshotAsync();
            
            if (!snapshot.Exists)
            {
                return null;
            }

            var costDoc = snapshot.ConvertTo<CreditCostDocument>();
            return costDoc.ToCreditCost();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get credit cost by ID: {CostId}", id);
            throw;
        }
    }

    public async Task<CreditCost?> GetByOperationTypeAsync(string operationType)
    {
        try
        {
            var query = _context.CreditCosts
                .WhereEqualTo("operationType", operationType)
                .WhereEqualTo("isActive", true)
                .Limit(1);
            
            var snapshot = await query.GetSnapshotAsync();
            
            if (snapshot.Count == 0)
            {
                return null;
            }

            var costDoc = snapshot.Documents.First().ConvertTo<CreditCostDocument>();
            return costDoc.ToCreditCost();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get credit cost by operation type: {OperationType}", operationType);
            throw;
        }
    }

    public async Task<IEnumerable<CreditCost>> GetAllAsync()
    {
        try
        {
            var snapshot = await _context.CreditCosts.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<CreditCostDocument>().ToCreditCost());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all credit costs");
            throw;
        }
    }

    public async Task<IEnumerable<CreditCost>> GetActiveAsync()
    {
        try
        {
            var query = _context.CreditCosts.WhereEqualTo("isActive", true);
            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<CreditCostDocument>().ToCreditCost());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get active credit costs");
            throw;
        }
    }

    public async Task<CreditCost> AddAsync(CreditCost entity)
    {
        try
        {
            var costDoc = CreditCostDocument.FromCreditCost(entity);
            var docRef = _context.CreditCosts.Document(entity.Id);
            await docRef.SetAsync(costDoc);
            
            _logger.LogInformation("Added credit cost: {CostId}", entity.Id);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add credit cost: {CostId}", entity.Id);
            throw;
        }
    }

    public async Task<CreditCost> UpdateAsync(CreditCost entity)
    {
        try
        {
            var costDoc = CreditCostDocument.FromCreditCost(entity);
            var docRef = _context.CreditCosts.Document(entity.Id);
            await docRef.SetAsync(costDoc, SetOptions.MergeAll);
            
            _logger.LogInformation("Updated credit cost: {CostId}", entity.Id);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update credit cost: {CostId}", entity.Id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(string id)
    {
        try
        {
            var docRef = _context.CreditCosts.Document(id);
            await docRef.DeleteAsync();
            
            _logger.LogInformation("Deleted credit cost: {CostId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete credit cost: {CostId}", id);
            return false;
        }
    }

    public async Task<bool> ExistsAsync(string id)
    {
        try
        {
            var docRef = _context.CreditCosts.Document(id);
            var snapshot = await docRef.GetSnapshotAsync();
            return snapshot.Exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if credit cost exists: {CostId}", id);
            throw;
        }
    }

    public async Task<bool> UpdateCostAsync(string operationType, int newCost)
    {
        try
        {
            var query = _context.CreditCosts
                .WhereEqualTo("operationType", operationType)
                .WhereEqualTo("isActive", true)
                .Limit(1);
            
            var snapshot = await query.GetSnapshotAsync();
            
            if (snapshot.Count == 0)
            {
                _logger.LogWarning("No active credit cost found for operation type: {OperationType}", operationType);
                return false;
            }

            var docRef = snapshot.Documents.First().Reference;
            await docRef.UpdateAsync(new Dictionary<string, object>
            {
                { "cost", newCost },
                { "updatedAt", Timestamp.GetCurrentTimestamp() }
            });
            
            _logger.LogInformation("Updated cost for operation type {OperationType} to {NewCost}", operationType, newCost);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update cost for operation type: {OperationType}", operationType);
            return false;
        }
    }

    public async Task<bool> SetActiveStatusAsync(string id, bool isActive)
    {
        try
        {
            var docRef = _context.CreditCosts.Document(id);
            await docRef.UpdateAsync(new Dictionary<string, object>
            {
                { "isActive", isActive },
                { "updatedAt", Timestamp.GetCurrentTimestamp() }
            });
            
            _logger.LogInformation("Set active status for credit cost {CostId} to {IsActive}", id, isActive);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set active status for credit cost: {CostId}", id);
            return false;
        }
    }
}
