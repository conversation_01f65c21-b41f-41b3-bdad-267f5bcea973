using VidCompressor.Models;

namespace VidCompressor.Repositories;

/// <summary>
/// Repository interface for CreditTransaction entity operations
/// </summary>
public interface ICreditTransactionRepository : IRepository<CreditTransaction>
{
    /// <summary>
    /// Get credit transactions for a specific user
    /// </summary>
    Task<IEnumerable<CreditTransaction>> GetByUserIdAsync(string userId, int limit = 50);

    /// <summary>
    /// Get credit transactions by type
    /// </summary>
    Task<IEnumerable<CreditTransaction>> GetByTypeAsync(CreditTransactionType type);

    /// <summary>
    /// Get credit transactions for a specific compression job
    /// </summary>
    Task<IEnumerable<CreditTransaction>> GetByCompressionJobIdAsync(string compressionJobId);

    /// <summary>
    /// Get total credits used by a user
    /// </summary>
    Task<int> GetTotalCreditsUsedAsync(string userId);

    /// <summary>
    /// Get total credits purchased by a user
    /// </summary>
    Task<int> GetTotalCreditsPurchasedAsync(string userId);
}
