using VidCompressor.Models;

namespace VidCompressor.Repositories;

/// <summary>
/// Repository interface for CreditCost entity operations
/// </summary>
public interface ICreditCostRepository : IRepository<CreditCost>
{
    /// <summary>
    /// Get credit cost by operation type
    /// </summary>
    Task<CreditCost?> GetByOperationTypeAsync(string operationType);

    /// <summary>
    /// Get all active credit costs
    /// </summary>
    Task<IEnumerable<CreditCost>> GetActiveAsync();

    /// <summary>
    /// Update credit cost for an operation type
    /// </summary>
    Task<bool> UpdateCostAsync(string operationType, int newCost);

    /// <summary>
    /// Activate or deactivate a credit cost
    /// </summary>
    Task<bool> SetActiveStatusAsync(string id, bool isActive);
}
