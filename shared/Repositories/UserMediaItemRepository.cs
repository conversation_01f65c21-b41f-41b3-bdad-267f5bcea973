using Google.Cloud.Firestore;
using Microsoft.Extensions.Logging;
using VidCompressor.Data;
using VidCompressor.Models;
using VidCompressor.Models.Documents;

namespace VidCompressor.Repositories;

/// <summary>
/// Firestore implementation of UserMediaItem repository
/// </summary>
public class UserMediaItemRepository : IUserMediaItemRepository
{
    private readonly FirestoreDbContext _context;
    private readonly ILogger<UserMediaItemRepository> _logger;

    public UserMediaItemRepository(FirestoreDbContext context, ILogger<UserMediaItemRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<UserMediaItem?> GetByIdAsync(string id)
    {
        try
        {
            var docRef = _context.UserMediaItems.Document(id);
            var snapshot = await docRef.GetSnapshotAsync();
            
            if (!snapshot.Exists)
            {
                return null;
            }

            var itemDoc = snapshot.ConvertTo<UserMediaItemDocument>();
            return itemDoc.ToUserMediaItem();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user media item by ID: {ItemId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<UserMediaItem>> GetAllAsync()
    {
        try
        {
            var snapshot = await _context.UserMediaItems.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<UserMediaItemDocument>().ToUserMediaItem());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all user media items");
            throw;
        }
    }

    public async Task<IEnumerable<UserMediaItem>> GetByUserIdAsync(string userId)
    {
        try
        {
            var query = _context.UserMediaItems
                .WhereEqualTo("userId", userId)
                .OrderByDescending("lastAccessedAt");
            
            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<UserMediaItemDocument>().ToUserMediaItem());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user media items for user: {UserId}", userId);
            throw;
        }
    }

    public async Task<UserMediaItem?> GetByUserAndGoogleMediaItemIdAsync(string userId, string googleMediaItemId)
    {
        try
        {
            var query = _context.UserMediaItems
                .WhereEqualTo("userId", userId)
                .WhereEqualTo("googleMediaItemId", googleMediaItemId)
                .Limit(1);
            
            var snapshot = await query.GetSnapshotAsync();
            
            if (snapshot.Count == 0)
            {
                return null;
            }

            var itemDoc = snapshot.Documents.First().ConvertTo<UserMediaItemDocument>();
            return itemDoc.ToUserMediaItem();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user media item for user {UserId} and Google media item {GoogleMediaItemId}", userId, googleMediaItemId);
            throw;
        }
    }

    public async Task<IEnumerable<UserMediaItem>> GetByGoogleMediaItemIdsAsync(IEnumerable<string> googleMediaItemIds)
    {
        try
        {
            var mediaItemIdsList = googleMediaItemIds.ToList();
            if (!mediaItemIdsList.Any())
            {
                return Enumerable.Empty<UserMediaItem>();
            }

            // Firestore 'in' queries are limited to 10 items, so we need to batch
            var results = new List<UserMediaItem>();
            var batches = mediaItemIdsList.Chunk(10);

            foreach (var batch in batches)
            {
                var query = _context.UserMediaItems.WhereIn("googleMediaItemId", batch);
                var snapshot = await query.GetSnapshotAsync();
                var batchResults = snapshot.Documents.Select(doc => doc.ConvertTo<UserMediaItemDocument>().ToUserMediaItem());
                results.AddRange(batchResults);
            }

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user media items by Google media item IDs");
            throw;
        }
    }

    public async Task<UserMediaItem> AddAsync(UserMediaItem entity)
    {
        try
        {
            var itemDoc = UserMediaItemDocument.FromUserMediaItem(entity);
            var docRef = _context.UserMediaItems.Document(entity.Id);
            await docRef.SetAsync(itemDoc);
            
            _logger.LogInformation("Added user media item: {ItemId}", entity.Id);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add user media item: {ItemId}", entity.Id);
            throw;
        }
    }

    public async Task<UserMediaItem> UpdateAsync(UserMediaItem entity)
    {
        try
        {
            var itemDoc = UserMediaItemDocument.FromUserMediaItem(entity);
            var docRef = _context.UserMediaItems.Document(entity.Id);
            await docRef.SetAsync(itemDoc, SetOptions.MergeAll);
            
            _logger.LogInformation("Updated user media item: {ItemId}", entity.Id);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update user media item: {ItemId}", entity.Id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(string id)
    {
        try
        {
            var docRef = _context.UserMediaItems.Document(id);
            await docRef.DeleteAsync();
            
            _logger.LogInformation("Deleted user media item: {ItemId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete user media item: {ItemId}", id);
            return false;
        }
    }

    public async Task<bool> ExistsAsync(string id)
    {
        try
        {
            var docRef = _context.UserMediaItems.Document(id);
            var snapshot = await docRef.GetSnapshotAsync();
            return snapshot.Exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if user media item exists: {ItemId}", id);
            throw;
        }
    }

    public async Task<bool> UpdateLastAccessedAsync(IEnumerable<string> mediaItemIds)
    {
        try
        {
            var batch = _context.Database.StartBatch();
            var currentTime = Timestamp.GetCurrentTimestamp();

            foreach (var itemId in mediaItemIds)
            {
                var docRef = _context.UserMediaItems.Document(itemId);
                batch.Update(docRef, new Dictionary<string, object>
                {
                    { "lastAccessedAt", currentTime },
                    { "updatedAt", currentTime }
                });
            }

            await batch.CommitAsync();
            
            _logger.LogInformation("Updated last accessed time for {Count} media items", mediaItemIds.Count());
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update last accessed time for media items");
            return false;
        }
    }

    public async Task<int> DeleteOldMediaItemsAsync(DateTime cutoffDate)
    {
        try
        {
            var cutoffTimestamp = cutoffDate.ToTimestamp();
            var query = _context.UserMediaItems.WhereLessThan("lastAccessedAt", cutoffTimestamp);
            var snapshot = await query.GetSnapshotAsync();

            if (snapshot.Count == 0)
            {
                return 0;
            }

            var batch = _context.Database.StartBatch();
            foreach (var doc in snapshot.Documents)
            {
                batch.Delete(doc.Reference);
            }

            await batch.CommitAsync();
            
            _logger.LogInformation("Deleted {Count} old media items", snapshot.Count);
            return snapshot.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete old media items");
            throw;
        }
    }
}
