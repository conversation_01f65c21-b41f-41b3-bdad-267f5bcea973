using Google.Cloud.Firestore;
using Microsoft.Extensions.Logging;
using VidCompressor.Data;
using VidCompressor.Models;
using VidCompressor.Models.Documents;

namespace VidCompressor.Repositories;

/// <summary>
/// Firestore implementation of CreditTransaction repository
/// </summary>
public class CreditTransactionRepository : ICreditTransactionRepository
{
    private readonly FirestoreDbContext _context;
    private readonly ILogger<CreditTransactionRepository> _logger;

    public CreditTransactionRepository(FirestoreDbContext context, ILogger<CreditTransactionRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<CreditTransaction?> GetByIdAsync(string id)
    {
        try
        {
            var docRef = _context.CreditTransactions.Document(id);
            var snapshot = await docRef.GetSnapshotAsync();
            
            if (!snapshot.Exists)
            {
                return null;
            }

            var transactionDoc = snapshot.ConvertTo<CreditTransactionDocument>();
            return transactionDoc.ToCreditTransaction();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get credit transaction by ID: {TransactionId}", id);
            throw;
        }
    }

    public async Task<IEnumerable<CreditTransaction>> GetAllAsync()
    {
        try
        {
            var snapshot = await _context.CreditTransactions.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<CreditTransactionDocument>().ToCreditTransaction());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all credit transactions");
            throw;
        }
    }

    public async Task<IEnumerable<CreditTransaction>> GetByUserIdAsync(string userId, int limit = 50)
    {
        try
        {
            var query = _context.CreditTransactions
                .WhereEqualTo("userId", userId)
                .OrderByDescending("createdAt")
                .Limit(limit);
            
            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<CreditTransactionDocument>().ToCreditTransaction());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get credit transactions for user: {UserId}", userId);
            throw;
        }
    }

    public async Task<IEnumerable<CreditTransaction>> GetByTypeAsync(CreditTransactionType type)
    {
        try
        {
            var query = _context.CreditTransactions
                .WhereEqualTo("type", type.ToString())
                .OrderByDescending("createdAt");
            
            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<CreditTransactionDocument>().ToCreditTransaction());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get credit transactions by type: {Type}", type);
            throw;
        }
    }

    public async Task<IEnumerable<CreditTransaction>> GetByCompressionJobIdAsync(string compressionJobId)
    {
        try
        {
            var query = _context.CreditTransactions
                .WhereEqualTo("compressionJobId", compressionJobId)
                .OrderByDescending("createdAt");
            
            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents.Select(doc => doc.ConvertTo<CreditTransactionDocument>().ToCreditTransaction());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get credit transactions for compression job: {CompressionJobId}", compressionJobId);
            throw;
        }
    }

    public async Task<int> GetTotalCreditsUsedAsync(string userId)
    {
        try
        {
            var query = _context.CreditTransactions
                .WhereEqualTo("userId", userId)
                .WhereEqualTo("type", CreditTransactionType.Usage.ToString());
            
            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents
                .Select(doc => doc.ConvertTo<CreditTransactionDocument>())
                .Sum(t => Math.Abs(t.Amount)); // Usage amounts are negative, so we take absolute value
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get total credits used for user: {UserId}", userId);
            throw;
        }
    }

    public async Task<int> GetTotalCreditsPurchasedAsync(string userId)
    {
        try
        {
            var query = _context.CreditTransactions
                .WhereEqualTo("userId", userId)
                .WhereEqualTo("type", CreditTransactionType.Purchase.ToString());
            
            var snapshot = await query.GetSnapshotAsync();
            return snapshot.Documents
                .Select(doc => doc.ConvertTo<CreditTransactionDocument>())
                .Sum(t => t.Amount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get total credits purchased for user: {UserId}", userId);
            throw;
        }
    }

    public async Task<CreditTransaction> AddAsync(CreditTransaction entity)
    {
        try
        {
            var transactionDoc = CreditTransactionDocument.FromCreditTransaction(entity);
            var docRef = _context.CreditTransactions.Document(entity.Id);
            await docRef.SetAsync(transactionDoc);
            
            _logger.LogInformation("Added credit transaction: {TransactionId}", entity.Id);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add credit transaction: {TransactionId}", entity.Id);
            throw;
        }
    }

    public async Task<CreditTransaction> UpdateAsync(CreditTransaction entity)
    {
        try
        {
            var transactionDoc = CreditTransactionDocument.FromCreditTransaction(entity);
            var docRef = _context.CreditTransactions.Document(entity.Id);
            await docRef.SetAsync(transactionDoc, SetOptions.MergeAll);
            
            _logger.LogInformation("Updated credit transaction: {TransactionId}", entity.Id);
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update credit transaction: {TransactionId}", entity.Id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(string id)
    {
        try
        {
            var docRef = _context.CreditTransactions.Document(id);
            await docRef.DeleteAsync();
            
            _logger.LogInformation("Deleted credit transaction: {TransactionId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete credit transaction: {TransactionId}", id);
            return false;
        }
    }

    public async Task<bool> ExistsAsync(string id)
    {
        try
        {
            var docRef = _context.CreditTransactions.Document(id);
            var snapshot = await docRef.GetSnapshotAsync();
            return snapshot.Exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if credit transaction exists: {TransactionId}", id);
            throw;
        }
    }
}
