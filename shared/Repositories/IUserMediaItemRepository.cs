using VidCompressor.Models;

namespace VidCompressor.Repositories;

/// <summary>
/// Repository interface for UserMediaItem entity operations
/// </summary>
public interface IUserMediaItemRepository : IRepository<UserMediaItem>
{
    /// <summary>
    /// Get media items for a specific user
    /// </summary>
    Task<IEnumerable<UserMediaItem>> GetByUserIdAsync(string userId);

    /// <summary>
    /// Get media item by user ID and Google media item ID
    /// </summary>
    Task<UserMediaItem?> GetByUserAndGoogleMediaItemIdAsync(string userId, string googleMediaItemId);

    /// <summary>
    /// Update last accessed time for media items
    /// </summary>
    Task<bool> UpdateLastAccessedAsync(IEnumerable<string> mediaItemIds);

    /// <summary>
    /// Delete old media items that haven't been accessed recently
    /// </summary>
    Task<int> DeleteOldMediaItemsAsync(DateTime cutoffDate);

    /// <summary>
    /// Get media items by Google media item IDs
    /// </summary>
    Task<IEnumerable<UserMediaItem>> GetByGoogleMediaItemIdsAsync(IEnumerable<string> googleMediaItemIds);
}
