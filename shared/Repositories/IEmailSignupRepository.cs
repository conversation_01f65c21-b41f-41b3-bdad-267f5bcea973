using VidCompressor.Models;

namespace VidCompressor.Repositories;

/// <summary>
/// Repository interface for EmailSignup entity operations
/// </summary>
public interface IEmailSignupRepository : IRepository<EmailSignup>
{
    /// <summary>
    /// Get email signup by email address
    /// </summary>
    Task<EmailSignup?> GetByEmailAsync(string email);

    /// <summary>
    /// Get paginated list of email signups
    /// </summary>
    Task<(IEnumerable<EmailSignup> signups, int totalCount)> GetPaginatedAsync(int page, int pageSize);

    /// <summary>
    /// Get total count of email signups
    /// </summary>
    Task<int> GetCountAsync();

    /// <summary>
    /// Mark email signup as notified
    /// </summary>
    Task<bool> MarkAsNotifiedAsync(string id);
}
