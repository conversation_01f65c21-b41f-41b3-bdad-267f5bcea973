using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;

namespace VidCompressor.Services;

/// <summary>
/// Service for caching Google Photos preview images in cloud storage
/// </summary>
public class PreviewCacheService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly GoogleCloudStorageService _storageService;
    private readonly ILogger<PreviewCacheService> _logger;

    public PreviewCacheService(
        IHttpClientFactory httpClientFactory,
        GoogleCloudStorageService storageService,
        ILogger<PreviewCacheService> logger)
    {
        _httpClientFactory = httpClientFactory;
        _storageService = storageService;
        _logger = logger;
    }

    /// <summary>
    /// Fetches a preview image from Google Photos and caches it in cloud storage
    /// </summary>
    /// <param name="accessToken">Google access token for the user</param>
    /// <param name="mediaItemId">Media item ID</param>
    /// <param name="baseUrl">Base URL from PhotosPicker API</param>
    /// <param name="width">Preview width (default: 200)</param>
    /// <param name="height">Preview height (default: 200)</param>
    /// <param name="crop">Whether to crop the preview (default: true)</param>
    /// <returns>Cloud storage path of the cached preview image</returns>
    public async Task<string?> CachePreviewImageAsync(
        string accessToken, 
        string mediaItemId, 
        string? baseUrl = null, 
        int width = 200, 
        int height = 200, 
        bool crop = true)
    {
        try
        {
            _logger.LogInformation("Caching preview for media item {MediaItemId}", mediaItemId);

            // If no baseUrl provided, we can't fetch the preview
            if (string.IsNullOrEmpty(baseUrl))
            {
                _logger.LogWarning("No baseUrl provided for media item {MediaItemId}, cannot cache preview", mediaItemId);
                return null;
            }

            // Construct the preview URL using the BaseUrl (same logic as VideosController)
            string previewUrl = $"{baseUrl}=w{width}-h{height}";
            if (crop)
            {
                previewUrl += "-c";
            }

            _logger.LogDebug("Fetching preview from Google Photos: {PreviewUrl}", previewUrl);

            // Make an authenticated HTTP request to Google Photos for the preview image
            var httpClient = _httpClientFactory.CreateClient();
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            var imageResponse = await httpClient.GetAsync(previewUrl);

            if (!imageResponse.IsSuccessStatusCode)
            {
                var errorContent = await imageResponse.Content.ReadAsStringAsync();
                _logger.LogError("Failed to fetch preview from Google Photos for media item {MediaItemId}: {StatusCode} - {Error}", 
                    mediaItemId, imageResponse.StatusCode, errorContent);
                return null;
            }

            // Get the image stream and content type
            var imageStream = await imageResponse.Content.ReadAsStreamAsync();
            var contentType = imageResponse.Content.Headers.ContentType?.ToString() ?? "image/jpeg";

            // Determine file extension from content type
            var extension = GetFileExtensionFromContentType(contentType);

            // Generate a unique filename for the preview
            var previewFileName = $"preview_{mediaItemId}_{DateTime.UtcNow:yyyyMMdd_HHmmss}{extension}";

            // Upload the preview image to cloud storage
            var previewPath = await _storageService.UploadPreviewImageAsync(imageStream, previewFileName);

            _logger.LogInformation("Successfully cached preview for media item {MediaItemId} at {PreviewPath}", 
                mediaItemId, previewPath);

            return previewPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cache preview for media item {MediaItemId}", mediaItemId);
            return null;
        }
    }

    /// <summary>
    /// Gets the appropriate file extension based on content type
    /// </summary>
    private static string GetFileExtensionFromContentType(string contentType)
    {
        return contentType.ToLower() switch
        {
            "image/jpeg" => ".jpg",
            "image/jpg" => ".jpg",
            "image/png" => ".png",
            "image/webp" => ".webp",
            "image/gif" => ".gif",
            _ => ".jpg" // Default to jpg
        };
    }
}
