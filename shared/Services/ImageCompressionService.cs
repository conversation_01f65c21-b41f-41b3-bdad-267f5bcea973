using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Formats.Png;
using SixLabors.ImageSharp.Formats.Webp;
using SixLabors.ImageSharp.Processing;

namespace VidCompressor.Services;

public class ImageCompressionService
{
    private readonly ILogger<ImageCompressionService> _logger;
    private readonly MediaMetadataService _metadataService;

    public ImageCompressionService(ILogger<ImageCompressionService> logger, MediaMetadataService metadataService)
    {
        _logger = logger;
        _metadataService = metadataService;
    }

    /// <summary>
    /// Compresses an image based on the specified quality setting
    /// </summary>
    /// <param name="inputStream">Input image stream</param>
    /// <param name="outputStream">Output stream for compressed image</param>
    /// <param name="quality">Quality setting: "high", "medium", or "low"</param>
    /// <param name="originalWidth">Original image width (optional)</param>
    /// <param name="originalHeight">Original image height (optional)</param>
    /// <param name="preserveMetadata">Whether to preserve original metadata (default: true)</param>
    /// <returns>Compression statistics</returns>
    public async Task<ImageCompressionResult> CompressImageAsync(
        Stream inputStream,
        Stream outputStream,
        string quality,
        int? originalWidth = null,
        int? originalHeight = null,
        bool preserveMetadata = true)
    {
        _logger.LogInformation("Starting image compression with quality: {Quality}, preserveMetadata: {PreserveMetadata}", quality, preserveMetadata);

        var originalSize = inputStream.Length;
        inputStream.Position = 0;

        // Extract metadata before compression if preservation is enabled
        PhotoMetadataInfo? originalMetadata = null;
        if (preserveMetadata)
        {
            try
            {
                originalMetadata = await _metadataService.ExtractPhotoMetadataAsync(inputStream);
                inputStream.Position = 0; // Reset for image loading
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract metadata, continuing without metadata preservation");
            }
        }

        using var image = await Image.LoadAsync(inputStream);
        
        var actualWidth = image.Width;
        var actualHeight = image.Height;
        
        _logger.LogInformation("Original image dimensions: {Width}x{Height}", actualWidth, actualHeight);

        // Apply compression settings based on quality
        var compressionSettings = GetCompressionSettings(quality);
        
        // Resize if needed based on quality settings
        if (compressionSettings.MaxDimension.HasValue)
        {
            var maxDimension = compressionSettings.MaxDimension.Value;
            if (actualWidth > maxDimension || actualHeight > maxDimension)
            {
                var ratio = Math.Min((double)maxDimension / actualWidth, (double)maxDimension / actualHeight);
                var newWidth = (int)(actualWidth * ratio);
                var newHeight = (int)(actualHeight * ratio);
                
                _logger.LogInformation("Resizing image from {OriginalWidth}x{OriginalHeight} to {NewWidth}x{NewHeight}", 
                    actualWidth, actualHeight, newWidth, newHeight);
                
                image.Mutate(x => x.Resize(newWidth, newHeight));
            }
        }

        // Save with appropriate format and quality
        if (preserveMetadata && originalMetadata != null)
        {
            // Save to temporary stream first, then apply metadata
            using var tempStream = new MemoryStream();

            if (compressionSettings.OutputFormat == "webp")
            {
                var webpEncoder = new WebpEncoder
                {
                    Quality = compressionSettings.JpegQuality
                };
                await image.SaveAsync(tempStream, webpEncoder);
            }
            else if (compressionSettings.OutputFormat == "jpeg")
            {
                var jpegEncoder = new JpegEncoder
                {
                    Quality = compressionSettings.JpegQuality
                };
                await image.SaveAsync(tempStream, jpegEncoder);
            }
            else
            {
                // Default to JPEG for compression
                var jpegEncoder = new JpegEncoder
                {
                    Quality = compressionSettings.JpegQuality
                };
                await image.SaveAsync(tempStream, jpegEncoder);
            }

            // Apply metadata to the compressed image
            tempStream.Position = 0;
            await _metadataService.ApplyPhotoMetadataAsync(tempStream, originalMetadata, outputStream);
        }
        else
        {
            // Save without metadata preservation
            if (compressionSettings.OutputFormat == "webp")
            {
                var webpEncoder = new WebpEncoder
                {
                    Quality = compressionSettings.JpegQuality
                };
                await image.SaveAsync(outputStream, webpEncoder);
            }
            else if (compressionSettings.OutputFormat == "jpeg")
            {
                var jpegEncoder = new JpegEncoder
                {
                    Quality = compressionSettings.JpegQuality
                };
                await image.SaveAsync(outputStream, jpegEncoder);
            }
            else
            {
                // Default to JPEG for compression
                var jpegEncoder = new JpegEncoder
                {
                    Quality = compressionSettings.JpegQuality
                };
                await image.SaveAsync(outputStream, jpegEncoder);
            }
        }

        var compressedSize = outputStream.Length;
        var compressionRatio = originalSize > 0 ? (double)compressedSize / originalSize : 0;

        _logger.LogInformation("Image compression completed. Original: {OriginalSize} bytes, Compressed: {CompressedSize} bytes, Ratio: {Ratio:P2}",
            originalSize, compressedSize, compressionRatio);

        return new ImageCompressionResult
        {
            OriginalSizeBytes = originalSize,
            CompressedSizeBytes = compressedSize,
            CompressionRatio = compressionRatio,
            OriginalWidth = actualWidth,
            OriginalHeight = actualHeight,
            CompressedWidth = image.Width,
            CompressedHeight = image.Height
        };
    }

    private CompressionSettings GetCompressionSettings(string quality)
    {
        return quality.ToLower() switch
        {
            "high" => new CompressionSettings
            {
                JpegQuality = 85,
                MaxDimension = null, // Keep original size
                OutputFormat = "jpeg"
            },
            "medium" => new CompressionSettings
            {
                JpegQuality = 75,
                MaxDimension = 2048, // Max 2048px on longest side
                OutputFormat = "jpeg"
            },
            "low" => new CompressionSettings
            {
                JpegQuality = 60,
                MaxDimension = 1024, // Max 1024px on longest side
                OutputFormat = "webp" // Use WebP for better compression
            },
            _ => new CompressionSettings
            {
                JpegQuality = 75,
                MaxDimension = 2048,
                OutputFormat = "jpeg"
            }
        };
    }

    private class CompressionSettings
    {
        public int JpegQuality { get; set; }
        public int? MaxDimension { get; set; }
        public string OutputFormat { get; set; } = "jpeg";
    }
}

public class ImageCompressionResult
{
    public long OriginalSizeBytes { get; set; }
    public long CompressedSizeBytes { get; set; }
    public double CompressionRatio { get; set; }
    public int OriginalWidth { get; set; }
    public int OriginalHeight { get; set; }
    public int CompressedWidth { get; set; }
    public int CompressedHeight { get; set; }
}
