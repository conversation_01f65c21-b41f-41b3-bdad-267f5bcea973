using Google.Cloud.Tasks.V2;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;

namespace VidCompressor.Services;

public class CloudTasksService
{
    private readonly CloudTasksClient _client;
    private readonly CloudTasksConfig _config;
    private readonly ILogger<CloudTasksService> _logger;

    public CloudTasksService(
        IOptions<GoogleCloudConfig> config,
        ILogger<CloudTasksService> logger)
    {
        _config = config.Value.CloudTasks;
        _logger = logger;
        _client = CloudTasksClient.Create();
    }

    public async System.Threading.Tasks.Task EnqueueCompressionJobAsync(string jobId)
    {
        try
        {
            var queueName = QueueName.FromProjectLocationQueue(
                _config.ProjectId, 
                _config.Location, 
                _config.QueueName);

            var serviceAccountEmail = "<EMAIL>";

            var cloudTask = new Google.Cloud.Tasks.V2.Task
            {
                HttpRequest = new HttpRequest
                {
                    HttpMethod = Google.Cloud.Tasks.V2.HttpMethod.Post,
                    Url = $"{_config.HandlerUrl}/api/internal/process-compression-job",
                    Headers = { ["Content-Type"] = "application/json" },
                    Body = Google.Protobuf.ByteString.CopyFromUtf8(
                        JsonSerializer.Serialize(new { jobId })),
                    OidcToken = new OidcToken()
                    {
                        ServiceAccountEmail = serviceAccountEmail
                    },
                }
            };

            var response = await _client.CreateTaskAsync(queueName, cloudTask);
            _logger.LogInformation("Enqueued compression job {JobId} as task {TaskName}", 
                jobId, response.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to enqueue compression job {JobId}", jobId);
            throw;
        }
    }
}


