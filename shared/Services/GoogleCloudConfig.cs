namespace VidCompressor.Services;

public class GoogleCloudConfig
{
    public string ProjectId { get; set; } = string.Empty;
    public string Region { get; set; } = "us-central1";
    public string InputBucketName { get; set; } = string.Empty;
    public string OutputBucketName { get; set; } = string.Empty;
    public string ServiceAccountKeyPath { get; set; } = string.Empty;
    public TranscoderConfig Transcoder { get; set; } = new();
    public CloudTasksConfig CloudTasks { get; set; } = new();
}

public class TranscoderConfig
{
    public string Location { get; set; } = "us-central1";
    public Dictionary<string, TranscoderPreset> Presets { get; set; } = new()
    {
        ["high"] = new TranscoderPreset
        {
            VideoBitrate = 8000000, // 8 Mbps
            AudioBitrate = 128000,  // 128 kbps
            Description = "High quality compression"
        },
        ["medium"] = new TranscoderPreset
        {
            VideoBitrate = 4000000, // 4 Mbps
            AudioBitrate = 128000,  // 128 kbps
            Description = "Balanced quality and size"
        },
        ["low"] = new TranscoderPreset
        {
            VideoBitrate = 2000000, // 2 Mbps
            AudioBitrate = 96000,   // 96 kbps
            Description = "Smaller file size"
        }
    };
}

public class TranscoderPreset
{
    public int VideoBitrate { get; set; }
    public int AudioBitrate { get; set; }
    public string Description { get; set; } = string.Empty;
    public string VideoCodec { get; set; } = "h264";
    public string AudioCodec { get; set; } = "aac";
    public string Container { get; set; } = "mp4";
}

public class CloudTasksConfig
{
    public string ProjectId { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string QueueName { get; set; } = "video-compression-jobs";
    public string HandlerUrl { get; set; } = string.Empty;
}
