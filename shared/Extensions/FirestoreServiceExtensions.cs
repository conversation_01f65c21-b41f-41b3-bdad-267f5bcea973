using Google.Cloud.Firestore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using VidCompressor.Configuration;
using VidCompressor.Data;

namespace VidCompressor.Extensions;

/// <summary>
/// Extension methods for configuring Firestore services
/// </summary>
public static class FirestoreServiceExtensions
{
    /// <summary>
    /// Add Firestore services to the dependency injection container
    /// </summary>
    public static IServiceCollection AddFirestore(this IServiceCollection services, IConfiguration configuration)
    {
        var firestoreConfig = configuration.GetSection(FirestoreConfiguration.SectionName).Get<FirestoreConfiguration>()
            ?? throw new InvalidOperationException("Firestore configuration is required");

        // Validate required configuration
        if (string.IsNullOrEmpty(firestoreConfig.ProjectId))
        {
            throw new InvalidOperationException("Firestore ProjectId is required");
        }

        // Register Firestore configuration
        services.Configure<FirestoreConfiguration>(configuration.GetSection(FirestoreConfiguration.SectionName));

        // Configure environment variables before creating FirestoreDb
        if (firestoreConfig.UseEmulator && !string.IsNullOrEmpty(firestoreConfig.EmulatorHost))
        {
            Environment.SetEnvironmentVariable("FIRESTORE_EMULATOR_HOST", firestoreConfig.EmulatorHost);
            Console.WriteLine($"[FIRESTORE] Setting emulator host to: {firestoreConfig.EmulatorHost}");
            Console.WriteLine($"[FIRESTORE] Environment variable set: {Environment.GetEnvironmentVariable("FIRESTORE_EMULATOR_HOST")}");
        }
        else
        {
            Console.WriteLine($"[FIRESTORE] Not using emulator. UseEmulator: {firestoreConfig.UseEmulator}, EmulatorHost: {firestoreConfig.EmulatorHost}");
        }

        // Configure service account key if specified
        if (!string.IsNullOrEmpty(firestoreConfig.ServiceAccountKeyPath))
        {
            Environment.SetEnvironmentVariable("GOOGLE_APPLICATION_CREDENTIALS", firestoreConfig.ServiceAccountKeyPath);
        }

        // Register FirestoreDb
        services.AddSingleton<FirestoreDb>(serviceProvider =>
        {
            Console.WriteLine($"[FIRESTORE] Creating FirestoreDb with ProjectId: {firestoreConfig.ProjectId}");
            Console.WriteLine($"[FIRESTORE] Current FIRESTORE_EMULATOR_HOST: {Environment.GetEnvironmentVariable("FIRESTORE_EMULATOR_HOST")}");

            var builder = new FirestoreDbBuilder
            {
                ProjectId = firestoreConfig.ProjectId,
                DatabaseId = firestoreConfig.DatabaseId
            };

            var db = builder.Build();
            Console.WriteLine($"[FIRESTORE] FirestoreDb created successfully");
            return db;
        });

        // Register Firestore context
        services.AddScoped<FirestoreDbContext>();

        // Register repositories
        services.AddScoped<VidCompressor.Repositories.IUserRepository, VidCompressor.Repositories.UserRepository>();
        services.AddScoped<VidCompressor.Repositories.ICompressionJobRepository, VidCompressor.Repositories.CompressionJobRepository>();
        services.AddScoped<VidCompressor.Repositories.IUserMediaItemRepository, VidCompressor.Repositories.UserMediaItemRepository>();
        services.AddScoped<VidCompressor.Repositories.ICreditTransactionRepository, VidCompressor.Repositories.CreditTransactionRepository>();
        services.AddScoped<VidCompressor.Repositories.ICreditCostRepository, VidCompressor.Repositories.CreditCostRepository>();
        services.AddScoped<VidCompressor.Repositories.IEmailSignupRepository, VidCompressor.Repositories.EmailSignupRepository>();

        return services;
    }
}
