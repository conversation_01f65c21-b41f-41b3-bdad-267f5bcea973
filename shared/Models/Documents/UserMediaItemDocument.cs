using Google.Cloud.Firestore;

namespace VidCompressor.Models.Documents;

/// <summary>
/// Firestore document model for UserMediaItem entity
/// </summary>
[FirestoreData]
public class UserMediaItemDocument
{
    [FirestoreProperty("id")]
    public string Id { get; set; } = string.Empty;

    [FirestoreProperty("userId")]
    public string UserId { get; set; } = string.Empty;

    [FirestoreProperty("googleMediaItemId")]
    public string GoogleMediaItemId { get; set; } = string.Empty;

    [FirestoreProperty("filename")]
    public string Filename { get; set; } = string.Empty;

    [FirestoreProperty("mimeType")]
    public string MimeType { get; set; } = string.Empty;

    [FirestoreProperty("baseUrl")]
    public string BaseUrl { get; set; } = string.Empty;

    [FirestoreProperty("mediaType")]
    public string MediaType { get; set; } = string.Empty;

    [FirestoreProperty("width")]
    public int? Width { get; set; }

    [FirestoreProperty("height")]
    public int? Height { get; set; }

    [FirestoreProperty("fileSizeBytes")]
    public long? FileSizeBytes { get; set; }

    [FirestoreProperty("creationTime")]
    public Timestamp? CreationTime { get; set; }

    [FirestoreProperty("googlePhotosUrl")]
    public string? GooglePhotosUrl { get; set; }

    [FirestoreProperty("metadata")]
    public string? Metadata { get; set; }

    [FirestoreProperty("addedAt")]
    public Timestamp AddedAt { get; set; } = Timestamp.GetCurrentTimestamp();

    [FirestoreProperty("lastAccessedAt")]
    public Timestamp LastAccessedAt { get; set; } = Timestamp.GetCurrentTimestamp();

    [FirestoreProperty("updatedAt")]
    public Timestamp UpdatedAt { get; set; } = Timestamp.GetCurrentTimestamp();

    /// <summary>
    /// Convert from EF Core UserMediaItem model to Firestore document
    /// </summary>
    public static UserMediaItemDocument FromUserMediaItem(UserMediaItem item)
    {
        return new UserMediaItemDocument
        {
            Id = item.Id,
            UserId = item.UserId,
            GoogleMediaItemId = item.GoogleMediaItemId,
            Filename = item.Filename,
            MimeType = item.MimeType,
            BaseUrl = item.BaseUrl,
            MediaType = item.MediaType.ToString(),
            Width = item.Width,
            Height = item.Height,
            FileSizeBytes = item.FileSizeBytes,
            CreationTime = item.CreationTime?.ToTimestamp(),
            GooglePhotosUrl = item.GooglePhotosUrl,
            Metadata = item.Metadata,
            AddedAt = item.AddedAt.ToTimestamp(),
            LastAccessedAt = item.LastAccessedAt.ToTimestamp(),
            UpdatedAt = Timestamp.GetCurrentTimestamp()
        };
    }

    /// <summary>
    /// Convert from Firestore document to EF Core UserMediaItem model
    /// </summary>
    public UserMediaItem ToUserMediaItem()
    {
        return new UserMediaItem
        {
            Id = Id,
            UserId = UserId,
            GoogleMediaItemId = GoogleMediaItemId,
            Filename = Filename,
            MimeType = MimeType,
            BaseUrl = BaseUrl,
            MediaType = Enum.Parse<Models.MediaType>(MediaType),
            Width = Width,
            Height = Height,
            FileSizeBytes = FileSizeBytes,
            CreationTime = CreationTime?.ToDateTime(),
            GooglePhotosUrl = GooglePhotosUrl,
            Metadata = Metadata,
            AddedAt = AddedAt.ToDateTime(),
            LastAccessedAt = LastAccessedAt.ToDateTime()
        };
    }
}
