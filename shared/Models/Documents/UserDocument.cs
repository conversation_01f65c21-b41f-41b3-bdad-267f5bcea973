using Google.Cloud.Firestore;

namespace VidCompressor.Models.Documents;

/// <summary>
/// Firestore document model for User entity
/// </summary>
[FirestoreData]
public class UserDocument
{
    [FirestoreProperty("id")]
    public string Id { get; set; } = string.Empty;

    [FirestoreProperty("email")]
    public string Email { get; set; } = string.Empty;

    [FirestoreProperty("firstName")]
    public string? FirstName { get; set; }

    [FirestoreProperty("profilePictureUrl")]
    public string? ProfilePictureUrl { get; set; }

    [FirestoreProperty("subscriptionStatus")]
    public string? SubscriptionStatus { get; set; }

    [FirestoreProperty("credits")]
    public int Credits { get; set; } = 0;

    [FirestoreProperty("googleAccessToken")]
    public string? GoogleAccessToken { get; set; }

    [FirestoreProperty("googleTokenExpiry")]
    public Timestamp? GoogleTokenExpiry { get; set; }

    [FirestoreProperty("googleRefreshToken")]
    public string? GoogleRefreshToken { get; set; }

    [FirestoreProperty("defaultUploadToGooglePhotos")]
    public bool DefaultUploadToGooglePhotos { get; set; } = true;

    [FirestoreProperty("createdAt")]
    public Timestamp CreatedAt { get; set; } = Timestamp.GetCurrentTimestamp();

    [FirestoreProperty("updatedAt")]
    public Timestamp UpdatedAt { get; set; } = Timestamp.GetCurrentTimestamp();

    /// <summary>
    /// Convert from EF Core User model to Firestore document
    /// </summary>
    public static UserDocument FromUser(User user)
    {
        return new UserDocument
        {
            Id = user.Id,
            Email = user.Email,
            FirstName = user.FirstName,
            ProfilePictureUrl = user.ProfilePictureUrl,
            SubscriptionStatus = user.SubscriptionStatus,
            Credits = user.Credits,
            GoogleAccessToken = user.GoogleAccessToken,
            GoogleTokenExpiry = user.GoogleTokenExpiry?.ToTimestamp(),
            GoogleRefreshToken = user.GoogleRefreshToken,
            DefaultUploadToGooglePhotos = user.DefaultUploadToGooglePhotos,
            UpdatedAt = Timestamp.GetCurrentTimestamp()
        };
    }

    /// <summary>
    /// Convert from Firestore document to EF Core User model
    /// </summary>
    public User ToUser()
    {
        return new User
        {
            Id = Id,
            Email = Email,
            FirstName = FirstName,
            ProfilePictureUrl = ProfilePictureUrl,
            SubscriptionStatus = SubscriptionStatus,
            Credits = Credits,
            GoogleAccessToken = GoogleAccessToken,
            GoogleTokenExpiry = GoogleTokenExpiry?.ToDateTime(),
            GoogleRefreshToken = GoogleRefreshToken,
            DefaultUploadToGooglePhotos = DefaultUploadToGooglePhotos
        };
    }
}

/// <summary>
/// Extension methods for DateTime to Timestamp conversion
/// </summary>
public static class DateTimeExtensions
{
    public static Timestamp? ToTimestamp(this DateTime? dateTime)
    {
        return dateTime?.ToTimestamp();
    }

    public static Timestamp ToTimestamp(this DateTime dateTime)
    {
        return Timestamp.FromDateTime(dateTime.ToUniversalTime());
    }
}
