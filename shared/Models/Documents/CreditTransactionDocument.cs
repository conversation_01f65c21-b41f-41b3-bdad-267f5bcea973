using Google.Cloud.Firestore;

namespace VidCompressor.Models.Documents;

/// <summary>
/// Firestore document model for CreditTransaction entity
/// </summary>
[FirestoreData]
public class CreditTransactionDocument
{
    [FirestoreProperty("id")]
    public string Id { get; set; } = string.Empty;

    [FirestoreProperty("userId")]
    public string UserId { get; set; } = string.Empty;

    [FirestoreProperty("type")]
    public string Type { get; set; } = string.Empty;

    [FirestoreProperty("amount")]
    public int Amount { get; set; }

    [FirestoreProperty("balanceAfter")]
    public int BalanceAfter { get; set; }

    [FirestoreProperty("description")]
    public string Description { get; set; } = string.Empty;

    [FirestoreProperty("compressionJobId")]
    public string? CompressionJobId { get; set; }

    [FirestoreProperty("paymentId")]
    public string? PaymentId { get; set; }

    [FirestoreProperty("createdAt")]
    public Timestamp CreatedAt { get; set; } = Timestamp.GetCurrentTimestamp();

    /// <summary>
    /// Convert from EF Core CreditTransaction model to Firestore document
    /// </summary>
    public static CreditTransactionDocument FromCreditTransaction(CreditTransaction transaction)
    {
        return new CreditTransactionDocument
        {
            Id = transaction.Id,
            UserId = transaction.UserId,
            Type = transaction.Type.ToString(),
            Amount = transaction.Amount,
            BalanceAfter = transaction.BalanceAfter,
            Description = transaction.Description,
            CompressionJobId = transaction.CompressionJobId,
            PaymentId = transaction.PaymentId,
            CreatedAt = transaction.CreatedAt.ToTimestamp()
        };
    }

    /// <summary>
    /// Convert from Firestore document to EF Core CreditTransaction model
    /// </summary>
    public CreditTransaction ToCreditTransaction()
    {
        return new CreditTransaction
        {
            Id = Id,
            UserId = UserId,
            Type = Enum.Parse<CreditTransactionType>(Type),
            Amount = Amount,
            BalanceAfter = BalanceAfter,
            Description = Description,
            CompressionJobId = CompressionJobId,
            PaymentId = PaymentId,
            CreatedAt = CreatedAt.ToDateTime()
        };
    }
}
