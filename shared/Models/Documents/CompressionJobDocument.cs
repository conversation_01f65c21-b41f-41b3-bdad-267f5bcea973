using Google.Cloud.Firestore;

namespace VidCompressor.Models.Documents;

/// <summary>
/// Firestore document model for CompressionJob entity
/// </summary>
[FirestoreData]
public class CompressionJobDocument
{
    [FirestoreProperty("id")]
    public string Id { get; set; } = string.Empty;

    [FirestoreProperty("userId")]
    public string UserId { get; set; } = string.Empty;

    [FirestoreProperty("mediaItemId")]
    public string MediaItemId { get; set; } = string.Empty;

    [FirestoreProperty("mediaType")]
    public string MediaType { get; set; } = string.Empty;

    [FirestoreProperty("baseUrl")]
    public string? BaseUrl { get; set; }

    [FirestoreProperty("quality")]
    public string Quality { get; set; } = string.Empty;

    [FirestoreProperty("uploadToGooglePhotos")]
    public bool UploadToGooglePhotos { get; set; } = true;

    [FirestoreProperty("status")]
    public string Status { get; set; } = string.Empty;

    [FirestoreProperty("inputStoragePath")]
    public string? InputStoragePath { get; set; }

    [FirestoreProperty("outputStoragePath")]
    public string? OutputStoragePath { get; set; }

    [FirestoreProperty("transcoderJobName")]
    public string? TranscoderJobName { get; set; }

    [FirestoreProperty("compressedFilePath")]
    public string? CompressedFilePath { get; set; }

    [FirestoreProperty("errorMessage")]
    public string? ErrorMessage { get; set; }

    [FirestoreProperty("createdAt")]
    public Timestamp CreatedAt { get; set; } = Timestamp.GetCurrentTimestamp();

    [FirestoreProperty("startedAt")]
    public Timestamp? StartedAt { get; set; }

    [FirestoreProperty("completedAt")]
    public Timestamp? CompletedAt { get; set; }

    [FirestoreProperty("originalSizeBytes")]
    public long? OriginalSizeBytes { get; set; }

    [FirestoreProperty("compressedSizeBytes")]
    public long? CompressedSizeBytes { get; set; }

    [FirestoreProperty("compressionRatio")]
    public double? CompressionRatio { get; set; }

    // DurationSeconds and UploadedMediaItemId properties removed as they're not in the current CompressionJob model

    [FirestoreProperty("compressedGooglePhotosUrl")]
    public string? CompressedGooglePhotosUrl { get; set; }

    [FirestoreProperty("creditsUsed")]
    public int? CreditsUsed { get; set; }

    [FirestoreProperty("previewImagePath")]
    public string? PreviewImagePath { get; set; }

    [FirestoreProperty("updatedAt")]
    public Timestamp UpdatedAt { get; set; } = Timestamp.GetCurrentTimestamp();

    /// <summary>
    /// Convert from EF Core CompressionJob model to Firestore document
    /// </summary>
    public static CompressionJobDocument FromCompressionJob(CompressionJob job)
    {
        return new CompressionJobDocument
        {
            Id = job.Id,
            UserId = job.UserId,
            MediaItemId = job.MediaItemId,
            MediaType = job.MediaType.ToString(),
            BaseUrl = job.BaseUrl,
            Quality = job.Quality,
            UploadToGooglePhotos = job.UploadToGooglePhotos,
            Status = job.Status.ToString(),
            InputStoragePath = job.InputStoragePath,
            OutputStoragePath = job.OutputStoragePath,
            TranscoderJobName = job.TranscoderJobName,
            CompressedFilePath = job.CompressedFilePath,
            ErrorMessage = job.ErrorMessage,
            CreatedAt = job.CreatedAt.ToTimestamp(),
            StartedAt = job.StartedAt?.ToTimestamp(),
            CompletedAt = job.CompletedAt?.ToTimestamp(),
            OriginalSizeBytes = job.OriginalSizeBytes,
            CompressedSizeBytes = job.CompressedSizeBytes,
            CompressionRatio = job.CompressionRatio,
            // DurationSeconds and UploadedMediaItemId are not part of the current CompressionJob model
            CompressedGooglePhotosUrl = job.CompressedGooglePhotosUrl,
            CreditsUsed = job.CreditsUsed,
            PreviewImagePath = job.PreviewImagePath,
            UpdatedAt = Timestamp.GetCurrentTimestamp()
        };
    }

    /// <summary>
    /// Convert from Firestore document to EF Core CompressionJob model
    /// </summary>
    public CompressionJob ToCompressionJob()
    {
        return new CompressionJob
        {
            Id = Id,
            UserId = UserId,
            MediaItemId = MediaItemId,
            MediaType = Enum.Parse<Models.MediaType>(MediaType),
            BaseUrl = BaseUrl,
            Quality = Quality,
            UploadToGooglePhotos = UploadToGooglePhotos,
            Status = Enum.Parse<CompressionJobStatus>(Status),
            InputStoragePath = InputStoragePath,
            OutputStoragePath = OutputStoragePath,
            TranscoderJobName = TranscoderJobName,
            CompressedFilePath = CompressedFilePath,
            ErrorMessage = ErrorMessage,
            CreatedAt = CreatedAt.ToDateTime(),
            StartedAt = StartedAt?.ToDateTime(),
            CompletedAt = CompletedAt?.ToDateTime(),
            OriginalSizeBytes = OriginalSizeBytes,
            CompressedSizeBytes = CompressedSizeBytes,
            CompressionRatio = CompressionRatio,
            // DurationSeconds and UploadedMediaItemId are not part of the current CompressionJob model
            CompressedGooglePhotosUrl = CompressedGooglePhotosUrl,
            CreditsUsed = CreditsUsed,
            PreviewImagePath = PreviewImagePath
        };
    }
}
