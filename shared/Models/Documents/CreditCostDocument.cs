using Google.Cloud.Firestore;

namespace VidCompressor.Models.Documents;

/// <summary>
/// Firestore document model for CreditCost entity
/// </summary>
[FirestoreData]
public class CreditCostDocument
{
    [FirestoreProperty("id")]
    public string Id { get; set; } = string.Empty;

    [FirestoreProperty("operationType")]
    public string OperationType { get; set; } = string.Empty;

    [FirestoreProperty("cost")]
    public int Cost { get; set; }

    [FirestoreProperty("unit")]
    public string Unit { get; set; } = string.Empty;

    [FirestoreProperty("description")]
    public string Description { get; set; } = string.Empty;

    [FirestoreProperty("isActive")]
    public bool IsActive { get; set; } = true;

    [FirestoreProperty("createdAt")]
    public Timestamp CreatedAt { get; set; } = Timestamp.GetCurrentTimestamp();

    [FirestoreProperty("updatedAt")]
    public Timestamp UpdatedAt { get; set; } = Timestamp.GetCurrentTimestamp();

    /// <summary>
    /// Convert from EF Core CreditCost model to Firestore document
    /// </summary>
    public static CreditCostDocument FromCreditCost(CreditCost cost)
    {
        return new CreditCostDocument
        {
            Id = cost.Id,
            OperationType = cost.OperationType,
            Cost = cost.Cost,
            Unit = cost.Unit,
            Description = cost.Description,
            IsActive = cost.IsActive,
            CreatedAt = cost.CreatedAt.ToTimestamp(),
            UpdatedAt = Timestamp.GetCurrentTimestamp()
        };
    }

    /// <summary>
    /// Convert from Firestore document to EF Core CreditCost model
    /// </summary>
    public CreditCost ToCreditCost()
    {
        return new CreditCost
        {
            Id = Id,
            OperationType = OperationType,
            Cost = Cost,
            Unit = Unit,
            Description = Description,
            IsActive = IsActive,
            CreatedAt = CreatedAt.ToDateTime()
        };
    }
}
