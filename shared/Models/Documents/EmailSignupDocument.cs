using Google.Cloud.Firestore;

namespace VidCompressor.Models.Documents;

/// <summary>
/// Firestore document model for EmailSignup entity
/// </summary>
[FirestoreData]
public class EmailSignupDocument
{
    [FirestoreProperty("id")]
    public string Id { get; set; } = string.Empty;

    [FirestoreProperty("email")]
    public string Email { get; set; } = string.Empty;

    [FirestoreProperty("createdAt")]
    public Timestamp CreatedAt { get; set; } = Timestamp.GetCurrentTimestamp();

    [FirestoreProperty("ipAddress")]
    public string? IpAddress { get; set; }

    [FirestoreProperty("userAgent")]
    public string? UserAgent { get; set; }

    [FirestoreProperty("isNotified")]
    public bool IsNotified { get; set; } = false;

    [FirestoreProperty("notifiedAt")]
    public Timestamp? NotifiedAt { get; set; }

    /// <summary>
    /// Convert from EF Core EmailSignup model to Firestore document
    /// </summary>
    public static EmailSignupDocument FromEmailSignup(EmailSignup signup)
    {
        return new EmailSignupDocument
        {
            Id = signup.Id.ToString(),
            Email = signup.Email,
            CreatedAt = signup.CreatedAt.ToTimestamp(),
            IpAddress = signup.IpAddress,
            UserAgent = signup.UserAgent,
            IsNotified = signup.IsNotified,
            NotifiedAt = signup.NotifiedAt?.ToTimestamp()
        };
    }

    /// <summary>
    /// Convert from Firestore document to EF Core EmailSignup model
    /// </summary>
    public EmailSignup ToEmailSignup()
    {
        return new EmailSignup
        {
            Id = int.Parse(Id),
            Email = Email,
            CreatedAt = CreatedAt.ToDateTime(),
            IpAddress = IpAddress,
            UserAgent = UserAgent,
            IsNotified = IsNotified,
            NotifiedAt = NotifiedAt?.ToDateTime()
        };
    }
}
