using Google.Cloud.SecretManager.V1;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;

namespace VidCompressor.Configuration;

/// <summary>
/// Configuration provider that loads secrets from Google Secret Manager at startup
/// </summary>
public class SecretManagerConfigurationProvider : ConfigurationProvider
{
    private readonly string _projectId;
    private readonly string _environment;
    private readonly ILogger<SecretManagerConfigurationProvider>? _logger;
    private readonly Dictionary<string, string> _secretMappings;

    public SecretManagerConfigurationProvider(string projectId, string environment, ILogger<SecretManagerConfigurationProvider>? logger = null)
    {
        _projectId = projectId;
        _environment = environment;
        _logger = logger;
        
        // Define mapping from configuration keys to secret names
        _secretMappings = new Dictionary<string, string>
        {
            { "Google:ClientSecret", $"google-client-secret-{environment}" },
            { "Stripe:SecretKey", $"stripe-secret-key-{environment}" },
            { "Stripe:WebhookSecret", $"stripe-webhook-secret-{environment}" },
            { "Stripe:PublishableKey", $"stripe-publishable-key-{environment}" }
            // Note: JWT secret removed - now using Google-signed JWTs
        };
    }

    public override void Load()
    {
        try
        {
            _logger?.LogInformation("Loading secrets from Google Secret Manager for environment: {Environment}", _environment);
            
            var client = SecretManagerServiceClient.Create();
            var loadedSecrets = new Dictionary<string, string?>();

            foreach (var mapping in _secretMappings)
            {
                var configKey = mapping.Key;
                var secretName = mapping.Value;
                
                try
                {
                    var request = new AccessSecretVersionRequest
                    {
                        Name = $"projects/{_projectId}/secrets/{secretName}/versions/latest"
                    };
                    var response = client.AccessSecretVersion(request);
                    var secretValue = response.Payload.Data.ToStringUtf8();

                    loadedSecrets[configKey] = secretValue;
                    _logger?.LogDebug("Successfully loaded secret: {SecretName} → {ConfigKey}", secretName, configKey);
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning("Failed to load secret {SecretName} for {ConfigKey}: {Error}", 
                        secretName, configKey, ex.Message);
                    
                    // For development, continue without the secret
                    // Production deployments should fail if secrets are missing
                    if (_environment == "prod" || _environment == "production")
                    {
                        throw new InvalidOperationException($"Required secret {secretName} not found in production environment", ex);
                    }
                }
            }

            Data = loadedSecrets;
            _logger?.LogInformation("Loaded {Count} secrets from Google Secret Manager", loadedSecrets.Count);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error loading secrets from Google Secret Manager");
            
            // In development, we can continue without secrets (fallback to appsettings)
            // In production, this should fail the startup
            if (_environment == "prod" || _environment == "production")
            {
                throw;
            }
            
            Data = new Dictionary<string, string?>();
        }
    }
}

/// <summary>
/// Configuration source for Google Secret Manager
/// </summary>
public class SecretManagerConfigurationSource : IConfigurationSource
{
    private readonly string _projectId;
    private readonly string _environment;
    private readonly ILogger<SecretManagerConfigurationProvider>? _logger;

    public SecretManagerConfigurationSource(string projectId, string environment, ILogger<SecretManagerConfigurationProvider>? logger = null)
    {
        _projectId = projectId;
        _environment = environment;
        _logger = logger;
    }

    public IConfigurationProvider Build(IConfigurationBuilder builder)
    {
        return new SecretManagerConfigurationProvider(_projectId, _environment, _logger);
    }
}

/// <summary>
/// Extension methods for adding Secret Manager configuration
/// </summary>
public static class SecretManagerConfigurationExtensions
{
    /// <summary>
    /// Add Google Secret Manager as a configuration source
    /// </summary>
    public static IConfigurationBuilder AddSecretManager(
        this IConfigurationBuilder builder, 
        string projectId, 
        string? environment = null,
        ILogger<SecretManagerConfigurationProvider>? logger = null)
    {
        // Determine environment from various sources
        environment ??= Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")?.ToLowerInvariant() switch
        {
            "development" => "dev",
            "staging" => "staging", 
            "production" => "prod",
            _ => "dev"
        };

        return builder.Add(new SecretManagerConfigurationSource(projectId, environment, logger));
    }

    /// <summary>
    /// Add Google Secret Manager with automatic project detection
    /// </summary>
    public static IConfigurationBuilder AddSecretManager(
        this IConfigurationBuilder builder,
        IConfiguration configuration,
        ILogger<SecretManagerConfigurationProvider>? logger = null)
    {
        var projectId = configuration["GoogleCloud:ProjectId"] 
            ?? Environment.GetEnvironmentVariable("GOOGLE_CLOUD_PROJECT")
            ?? throw new InvalidOperationException("Google Cloud Project ID not found in configuration or environment");

        return builder.AddSecretManager(projectId, logger: logger);
    }
}
