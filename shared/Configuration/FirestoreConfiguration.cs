namespace VidCompressor.Configuration;

/// <summary>
/// Configuration settings for Firestore database connection
/// </summary>
public class FirestoreConfiguration
{
    public const string SectionName = "Firestore";

    /// <summary>
    /// Google Cloud Project ID
    /// </summary>
    public string ProjectId { get; set; } = string.Empty;

    /// <summary>
    /// Path to the service account key file (optional, can use default credentials)
    /// </summary>
    public string? ServiceAccountKeyPath { get; set; }

    /// <summary>
    /// Database ID (default is "(default)")
    /// </summary>
    public string DatabaseId { get; set; } = "(default)";

    /// <summary>
    /// Whether to use emulator for local development
    /// </summary>
    public bool UseEmulator { get; set; } = false;

    /// <summary>
    /// Emulator host (e.g., "localhost:8080")
    /// </summary>
    public string? EmulatorHost { get; set; }
}
